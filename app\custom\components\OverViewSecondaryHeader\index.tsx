import Decimal from 'decimal.js';
import React, { useEffect, useState } from 'react'
import { EntityWithDetails } from '~/utils/db/entities/entities.db.server';
import { RowWithValues } from '~/utils/db/entities/rows.db.server';
import CircleStepper from '../CircleStepper/CircleStepper';



interface InfoItemProps {
  label: string;
  value: string;
}

interface InfoRowProps {
  leftLabel: string;
  leftValue: string;
  rightLabel: string;
  rightValue: string;
  className?: string;
}

interface TopbarProps {
  items: { label: string; value: string }[];
}

const Topbar: React.FC<TopbarProps> = ({ items }) => {
  const InfoItem: React.FC<InfoItemProps> = ({ label, value }) => {
    return (
      <div className="flex flex-wrap flex-1 shrink gap-0.5 items-start self-stretch my-auto basis-0 min-w-60 max-md:max-w-full">
        <div className="flex flex-1 shrink gap-2.5 items-center text-xs leading-5 text-gray-500 basis-0 min-w-60">
          <span className="self-stretch my-auto text-xs">{label}</span>
        </div>
        <div className="flex flex-1 shrink gap-2.5 items-center text-sm font-medium basis-0 min-w-60 text-neutral-800">
          <span className="flex-1 shrink self-stretch my-auto text-sm basis-0">
            {value}
          </span>
        </div>
      </div>
    );
  };

  const InfoRow: React.FC<InfoRowProps> = ({
    leftLabel,
    leftValue,
    rightLabel,
    rightValue,
    className = "",
  }) => {
    return (
      <div
        className={`flex flex-wrap gap-10 items-center w-full max-md:max-w-full ${className}`}
      >
        <InfoItem label={leftLabel} value={leftValue} />
        <InfoItem label={rightLabel} value={rightValue} />
      </div>
    );
  };

  return (
    <header className="flex flex-col justify-center py-5 rounded-lg w-full max-w-6xl">
      <div className="w-full max-md:max-w-full flex flex-col gap-3">
        {items.reduce<JSX.Element[]>((acc, item, index, arr) => {
          if (index % 2 !== 0) return acc;
          const next = arr[index + 1];
          acc.push(
            <InfoRow
              key={index}
              leftLabel={item.label}
              leftValue={item.value}
              rightLabel={next?.label ?? ""}
              rightValue={next?.value ?? ""}
            />
          );
          return acc;
        }, [])}
      </div>
    </header>
  );
};


const OverViewSecondaryHeader = ({ entity, item, rowData }: { entity: EntityWithDetails; item: RowWithValues; rowData: any }) => {
  const [secondaryHeader, setSecondaryHeader] = useState<{ label: string; value: string }[]>([]);

  function getTitleFromProperty(overviewHeaderPropertyId: string, item: RowWithValues) {
    const val = item.values.find((v) => v.propertyId === overviewHeaderPropertyId);
    if (!val) return "N/A";

    if (val.textValue !== null) return val.textValue;
    if (val.numberValue !== null) return val.numberValue;
    if (val.dateValue !== null) return new Date(val.dateValue).toLocaleDateString();

    if (val.booleanValue === false) return "No";
    if (val.booleanValue === true) return "Yes";

    if (val.multiple.length) {
      return val.multiple.map((m) => m.value).join(", ");
    }

    return "N/A";
  }

  useEffect(() => {
    const overViewSecondaryHeaderProperties = entity.properties.filter((p) => p.isOverviewSecondaryHeaderProperty);
    const mapped = overViewSecondaryHeaderProperties.map((property) => {
      return {
        label: property.title || property.name,
        value: getTitleFromProperty(property.id, item),
      };
    });
    setSecondaryHeader(mapped);
  }, [entity, item]);

  return (
    (secondaryHeader.length > 0 || rowData?.entity?.isOnboarding) && (
      <div className="flex justify-between items-center w-full bg-card">
        {secondaryHeader.length > 0 && <Topbar items={secondaryHeader} />}
      </div>
    )
  );
};

export default OverViewSecondaryHeader;