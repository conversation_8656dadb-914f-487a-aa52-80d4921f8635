import React from 'react'

const ActionOpenIcon = () => {
  return (
    <svg width="32" height="34" viewBox="0 0 32 34" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_d_8128_105522)">
      <mask id="path-1-inside-1_8128_105522" fill="white">
        <path d="M0 4C0 1.79086 1.79086 0 4 0H28C30.2091 0 32 1.79086 32 4V28C32 30.2091 30.2091 32 28 32H4C1.79086 32 0 30.2091 0 28V4Z" />
      </mask>
      <path d="M0 4C0 1.79086 1.79086 0 4 0H28C30.2091 0 32 1.79086 32 4V28C32 30.2091 30.2091 32 28 32H4C1.79086 32 0 30.2091 0 28V4Z" fill="white" shape-rendering="crispEdges" />
      <path d="M4 0V1H28V0V-1H4V0ZM32 4H31V28H32H33V4H32ZM28 32V31H4V32V33H28V32ZM0 28H1V4H0H-1V28H0ZM4 32V31C2.34315 31 1 29.6569 1 28H0H-1C-1 30.7614 1.23858 33 4 33V32ZM32 28H31C31 29.6569 29.6569 31 28 31V32V33C30.7614 33 33 30.7614 33 28H32ZM28 0V1C29.6569 1 31 2.34315 31 4H32H33C33 1.23858 30.7614 -1 28 -1V0ZM4 0V-1C1.23858 -1 -1 1.23858 -1 4H0H1C1 2.34315 2.34315 1 4 1V0Z" fill="#E6E6E6" mask="url(#path-1-inside-1_8128_105522)" />
      <path d="M22 20H13.3333V18.6667H22V20ZM22 16.6667H15.3333V15.3333H22V16.6667ZM22 12V13.3333H13.3333V12H22ZM10 18.3933L12.3867 16L10 13.6067L10.94 12.6667L14.2733 16L10.94 19.3333L10 18.3933Z" fill="#202229" />
    </g>
    <defs>
      <filter id="filter0_d_8128_105522" x="0" y="0" width="32" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset dy="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8128_105522" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8128_105522" result="shape" />
      </filter>
    </defs>
  </svg>
  )
}

export default ActionOpenIcon