// store/useActionToggleStore.ts
import { create } from 'zustand';

interface ActionToggleState {
  isActionOpen: boolean;
  toggleActionsBar: () => void;
  setIsActionOpen: (value: boolean) => void;
}

export const useActionToggle = create<ActionToggleState>((set, get) => ({
  isActionOpen: false,
  toggleActionsBar: () => {
    const current = get().isActionOpen;
    console.log('toggleActionsBar', current);
    set({ isActionOpen: !current });
  },
  setIsActionOpen: (value: boolean) => set({ isActionOpen: value }),
}));
