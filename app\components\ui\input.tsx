import { Clock } from "lucide-react";
import * as React from "react";

import { cn } from "~/lib/utils";

// function Input({ className, type, ...props }: React.ComponentProps<"input">) {
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, ...props }, ref: any) => {
  const [isInvalid, setIsInvalid] = React.useState(false);
  const isFirefox = typeof window !== 'undefined' && navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
  return (
    <div className="relative w-full">
      <input
        type={type}
        className={cn(
          "placeholder:text-muted-foreground focus-visible:ring-ring focus-visible:border-input flex h-9 w-full rounded-md bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
          className,
          "!border-input",
          `${isInvalid ? "!border-error !focus-visible:border-error !focus-visible:ring-error" : ""} border-top-disabled [&::-webkit-calendar-picker-indicator]:opacity-0`,
          type === "date" && "cursor-pointer"
        )}
        onInvalid={() => setIsInvalid(true)}
        onInput={() => setIsInvalid(false)}
        onClick={() => ref.current.showPicker()}
        ref={ref}
        {...props}
      />
      {type == "date" && !isInvalid && !isFirefox && (
        <div
          onClick={() => {
            ref.current.showPicker();
          }}
          className="absolute top-1/2 right-2 -translate-y-1/2 cursor-pointer"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" className="cursor-pointer" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M20 3H19V1H17V3H7V1H5V3H4C2.9 3 2 3.9 2 5V21C2 22.1 2.9 23 4 23H20C21.1 23 22 22.1 22 21V5C22 3.9 21.1 3 20 3ZM20 21H4V10H20V21ZM20 8H4V5H20V8Z"
              fill="#0A0501"
            />
          </svg>
        </div>
      )}

      {(type == "time" || type == "datetime-local") && !isInvalid && (
          <div className="absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer">
            <Clock className="text-muted-foreground h-4 w-4" />
          </div>
        )}

      {isInvalid && (
        <svg
          className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 1C4.13438 1 1 4.13438 1 8C1 11.8656 4.13438 15 8 15C11.8656 15 15 11.8656 15 8C15 4.13438 11.8656 1 8 1ZM10.5844 10.6594L9.55313 10.6547L8 8.80313L6.44844 10.6531L5.41563 10.6578C5.34688 10.6578 5.29063 10.6031 5.29063 10.5328C5.29063 10.5031 5.30156 10.475 5.32031 10.4516L7.35313 8.02969L5.32031 5.60938C5.30143 5.58647 5.29096 5.5578 5.29063 5.52812C5.29063 5.45937 5.34688 5.40312 5.41563 5.40312L6.44844 5.40781L8 7.25938L9.55156 5.40938L10.5828 5.40469C10.6516 5.40469 10.7078 5.45937 10.7078 5.52969C10.7078 5.55937 10.6969 5.5875 10.6781 5.61094L8.64844 8.03125L10.6797 10.4531C10.6984 10.4766 10.7094 10.5047 10.7094 10.5344C10.7094 10.6031 10.6531 10.6594 10.5844 10.6594Z"
            fill="#FF4D4F"
          />
        </svg>
      )}
    </div>
  );
});
Input.displayName = "Input";

export { Input };
