'use client';
import { Fragment, ReactNode, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useNavigation, useOutlet, useSearchParams, useParams, useActionData, useLocation } from "react-router";
import AccountOverviewSkeleton, { EntityFormSkeleton } from "~/components/ui/skeletons";
import EditPageLayout from "~/components/ui/layouts/EditPageLayout";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { getEntityPermission, getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { RowsA<PERSON> } from "~/utils/api/.server/RowsApi";
import SlideOverWideEmpty from "~/components/ui/slideOvers/SlideOverWideEmpty";
import { Rows_Overview } from "../routes/Rows_Overview.server";
import toast from "react-hot-toast";
import ActionResultModal from "~/components/ui/modals/ActionResultModal";
import RowOverviewHeader from "~/components/entities/rows/RowOverviewHeader";
import EntityDetails from "~/custom/components/RowOverviewRoute/components/EntityDetails";
import CircleStepper from "~/custom/components/CircleStepper/CircleStepper";
import OnboardingSessionBadge from "~/modules/onboarding/components/OnboardingSessionBadge";
import clsx from "clsx";
import { useIsMobile } from "~/hooks/use-mobile";
import { useActionToggle } from "~/hooks/useActionToggle";
type EditRowOptions = {
  hideTitle?: boolean;
  hideMenu?: boolean;
  hideShare?: boolean;
  hideTags?: boolean;
  hideTasks?: boolean;
  hideActivity?: boolean;
  disableUpdate?: boolean;
  disableDelete?: boolean;
};

interface Props {
  rowData: RowsApi.GetRowData;
  item: RowWithDetails;
  // permissions: RowPermission[];
  routes?: EntitiesApi.Routes;
  children?: ReactNode;
  title?: ReactNode;
  rowFormChildren?: ReactNode;
  afterRowForm?: ReactNode;
  options?: EditRowOptions;
  relationshipRows?: RowsApi.GetRelationshipRowsData;
  onSubmit?: (formData: FormData) => void;
}
export default function RowOverviewRoute({
  rowData,
  item,
  routes,
  children,
  title,
  rowFormChildren,
  afterRowForm,
  options,
  relationshipRows,
  onSubmit,
}: Props) {
  const { t } = useTranslation();
  const appOrAdminData = useAppOrAdminData();
  const params = useParams();
  const outlet = useOutlet();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const actionData = useActionData<Rows_Overview.ActionData>();
  const location = useLocation();

  const [showSkeleton, setShowSkeleton] = useState(false);

  useEffect(() => {

    if (rowData.item && navigation.state === "idle" && !navigation.location) {
      setShowSkeleton(true);

      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 700);

      return () => clearTimeout(timer);
    } else {
      setShowSkeleton(false);
    }
  }, [rowData.item?.id, navigation.state, navigation.location]);

  useEffect(() => {
    if (actionData?.success) {
      toast.success(actionData?.success);
    } else if (actionData?.error) {
      toast.error(actionData.error);
    }
  }, [actionData, t]);

  const isMobile = useIsMobile();
  const { isActionOpen } = useActionToggle();

  return (
    <>
      <EditPageLayout
        rowData={rowData}
        onSubmit={onSubmit}
        options={options}
        title={options?.hideTitle ? "" : t(rowData.entity.title)}
        menu={
          options?.hideMenu || !routes
            ? undefined
            : EntityHelper.getLayoutBreadcrumbsMenu({
              type: "overview",
              t,
              appOrAdminData,
              entity: rowData.entity,
              item: rowData.item,
              params,
              routes,
            })
        }
        withHome={false}
        isActionsBarLoading={showSkeleton}
      >
        {showSkeleton ? (
          <AccountOverviewSkeleton />
        ) : (
          <EditRow
            rowData={rowData}
            className={`mx-auto pb-10 transition-all duration-300 ease-in-out ${isMobile && isActionOpen ? "-translate-x-[90%]" : ""}`}
            item={item}
            routes={routes}
            title={title}
            options={options}
            rowFormChildren={rowFormChildren}
            afterRowForm={afterRowForm}
            onSubmit={onSubmit}
            relationshipRows={relationshipRows}
            actionData={actionData}
          >
            {children}
          </EditRow>
        )}
      </EditPageLayout>

      <SlideOverWideEmpty
        title={location.pathname.includes("share") ? t("shared.shareWith") : ""}
        open={!!outlet}
        onClose={() => {
          navigate(".", { replace: true });
        }}
        className="sm:max-w-sm"
        overflowYScroll={true}
        withBack={!location.pathname.includes("share")}
      >
        <div className={clsx(
          "-mx-1 -mt-3",
          location.pathname.includes("share") && "px-[18px] pt-4 h-full"
        )}>
          <div className={clsx("space-y-4", location.pathname.includes("share") && "h-full")}>
            {outlet}
          </div>
        </div>
      </SlideOverWideEmpty>

      <ActionResultModal actionData={actionData} showSuccess={false} showError={false} />
    </>
  );
}

interface EditRowProps {
  rowData: RowsApi.GetRowData;
  item: RowWithDetails;
  routes?: EntitiesApi.Routes;
  className: string;
  children?: ReactNode;
  title?: ReactNode;
  options?: EditRowOptions;
  rowFormChildren?: ReactNode;
  afterRowForm?: ReactNode;
  relationshipRows?: RowsApi.GetRelationshipRowsData;
  onSubmit?: (formData: FormData) => void;
  actionData: Rows_Overview.ActionData | null | undefined;
}
function EditRow({
  rowData,
  routes,
  item,
  className,
  title,
  options,
  children,
  rowFormChildren,
  afterRowForm,
  relationshipRows,
  onSubmit,
  actionData,
}: EditRowProps) {
  const appOrAdminData = useAppOrAdminData();
  const [searchParams] = useSearchParams();

  function canUpdate() {
    if (options?.disableUpdate) {
      // console.log("canUpdate: disableUpdate");
      return false;
    }
    if (!getUserHasPermission(appOrAdminData, getEntityPermission(rowData.entity, "update"))) {
      // console.log("canUpdate: no permission");
      return false;
    }
    if (!rowData.rowPermissions.canUpdate) {
      // console.log("canUpdate: rowPermissions.canUpdate");
      return false;
    }

    if (rowData?.item?.onboardingSession?.status == "completed") {
      return false;
    }
    return true;
  }
  function isEditing() {
    if (rowData.entity.onEdit === "overviewAlwaysEditable") {
      return true;
    }
    return searchParams.get("editing") !== null;
  }

  return (
    <Fragment

    >
      {!rowData.rowPermissions.canRead ? (
        <div className="font-medium">You don't have permissions to view this record.</div>
      ) : (
        <div className={className}>
          <RowOverviewHeader rowData={rowData} item={item} canUpdate={canUpdate()} isEditing={isEditing()} routes={routes} title={title} options={options} />

          <EntityDetails

            entity={rowData.entity}
            routes={routes}
            item={item}
            editing={isEditing()}
            linkedAccounts={[]}
            canDelete={false}
            canUpdate={canUpdate()}
            allEntities={appOrAdminData.entities}
            onSubmit={onSubmit ?? (() => { })}
            relationshipRows={relationshipRows || []}
            promptFlows={rowData.allPromptFlows}
          >
            {rowFormChildren}
          </EntityDetails>
          {/* {afterRowForm}  */}
        </div>
      )}
    </Fragment>
  );
}
