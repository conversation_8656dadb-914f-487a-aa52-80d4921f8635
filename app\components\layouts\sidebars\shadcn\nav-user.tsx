"use client";

import { Badge<PERSON><PERSON><PERSON>, Bell, ChevronRight, ChevronsUpDown, CreditCard, LogOut, Sparkles } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link, useFetcher, useLocation, useMatches, useParams, useSearchParams } from "react-router";
import { Fragment, useEffect, useRef } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  MenuDivider,
} from "~/components/ui/dropdown-menu";
import GearIcon from "~/components/ui/icons/GearIcon";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "~/components/ui/sidebar";
import UrlUtils from "~/utils/app/UrlUtils";
import UserUtils from "~/utils/app/UserUtils";
import { useRootData } from "~/utils/data/useRootData";
import AnalyticsHelper from "~/utils/helpers/AnalyticsHelper";
import { DarkModeSvgs } from "~/components/ui/toggles/DarkModeToggle";
import { DARK_MODE_IN_APP } from "~/application/Constants";
import { getUserHasPermission } from "~/utils/helpers/PermissionsHelper";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";

export function NavUser({
  user,
  layout,
  setIsDropdownOpen,
  isDropdownOpen,
}: {
  user: { email: string; firstName: string | null; lastName: string | null; avatar: string | null; admin?: { userId: string } | null };
  layout: "app" | "admin" | "docs";
  setIsDropdownOpen?: (value: boolean) => void;
  isDropdownOpen?: boolean;
}) {
  const { t } = useTranslation();
  const { isMobile, state, isHovering } = useSidebar();
  const isCollapsed = state === "collapsed";
  const { userSession } = useRootData();

  const params = useParams();
  const fetcher = useFetcher();
  let location = useLocation();
  const [searchParams] = useSearchParams();
  const rootData = useRootData();
  const matches = useMatches();
  const appOrAdminData = useAppOrAdminData();

  const onThemeToggle = async () => {
    const isDarkMode = userSession?.lightOrDarkMode === "dark";
    const form = new FormData();
    form.set("action", "toggleLightOrDarkMode");
    form.set("redirect", location.pathname + "?" + searchParams.toString());
    fetcher.submit(form, { method: "post", action: "/", preventScrollReset: true });

    const routeMatch = matches.find((m) => m.pathname == location.pathname);
    AnalyticsHelper.addEvent({
      url: location.pathname,
      route: routeMatch?.id,
      rootData,
      action: "toggleLightOrDarkMode",
      category: "user",
      label: "lightOrDarkMode",
      value: isDarkMode ? "light" : "dark",
    });
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen} modal={false}>
          <DropdownMenuTrigger asChild className="!px-3 !py-8 hover:rounded-none">
            <SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-2">
              <Avatar className="h-8 w-8 rounded-md">
                <AvatarImage src={user.avatar || ""} alt={UserUtils.fullName(user)} />
                <AvatarFallback className="bg-primary-light text-primary rounded-md">{UserUtils.avatarText(user)}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{UserUtils.fullName(user)}</span>
                <span className="text-muted-foreground truncate text-xs">{user.email}</span>
              </div>
              <ChevronRight className="h-[7px] w-[4px]" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className={`mb-1 ml-1 max-h-[520px] w-(--radix-dropdown-menu-trigger-width) min-w-60 rounded-lg ${isCollapsed ? "ml-5" : ""} ${isMobile ? "ml-[50%]" : ""}`}
            side={isMobile ? "bottom" : "right"}
            align="start"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-2 py-[12px] text-left text-sm">
                <Avatar className="h-8 w-8 rounded-md">
                  <AvatarImage src={user.avatar || ""} alt={UserUtils.fullName(user)} />
                  <AvatarFallback className="bg-primary-light text-primary rounded-md">{UserUtils.avatarText(user)}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{UserUtils.fullName(user)}</span>
                  <span className="text-muted-foreground truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>

            <div className="border-sidebar-border mx-1 border-t"></div>

            {layout === "app" ? (
              <Fragment>
                <DropdownMenuGroup className="py-1">
                  {/* <Link to={!params.tenant ? "" : UrlUtils.currentTenantUrl(params, `settings/profile?group=${params?.group}`)}> */}
                  <Link to={!params.tenant ? "" : UrlUtils.currentTenantUrl(params, `settings/profile${params?.group ? `?group=${params.group}` : ""}`)}>
                    <DropdownMenuItem
                      className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/profile`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary py-2 text-[13px]`}
                    >
                      {t("app.navbar.profileSettings")}
                    </DropdownMenuItem>
                  </Link>
                  {/* {getUserHasPermission(appOrAdminData, "app.settings.members.view") && (
                    <Link to={!params.tenant ? "" : UrlUtils.currentTenantUrl(params, "settings/members")}>
                      <DropdownMenuItem className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/members`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary text-[13px] py-2`}>
                        {t("app.navbar.members")}
                      </DropdownMenuItem>
                    </Link>
                  )}
                  <Link to={UrlUtils.currentTenantUrl(params, `settings/subscription`)}>
                    <DropdownMenuItem className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/subscription`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary text-[13px] py-2`}>
                      {t("app.navbar.subscription")}
                    </DropdownMenuItem>
                  </Link>
                  <Link to={UrlUtils.currentTenantUrl(params, `settings/account`)}>
                    <DropdownMenuItem className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/account`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary text-[13px] py-2`}>
                      {t("app.navbar.tenant")}
                    </DropdownMenuItem>
                  </Link>
                  {rootData.appConfiguration.app.features.tenantApiKeys && getUserHasPermission(appOrAdminData, "app.settings.apiKeys.view") && (
                    <Link to={!params.tenant ? "" : UrlUtils.currentTenantUrl(params, `settings/api`)}>
                      <DropdownMenuItem className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/api`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary text-[13px] py-2`}>
                        {t("models.apiKey.plural")}
                      </DropdownMenuItem>
                    </Link>
                  )}
                  {getUserHasPermission(appOrAdminData, "app.settings.auditTrails.view") && (
                    <Link to={!params.tenant ? "" : UrlUtils.currentTenantUrl(params, "settings/logs")}>
                      <DropdownMenuItem className={`${location.pathname === UrlUtils.currentTenantUrl(params, `settings/logs`) ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary text-[13px] py-2`}>
                        {t("models.log.plural")}
                      </DropdownMenuItem>
                    </Link>
                  )} */}
                </DropdownMenuGroup>

                <div className="border-sidebar-border mx-1 border-t"></div>

                <DropdownMenuLabel className="text-muted-foreground px-3 py-2 text-xs">{t("app.sidebar.apps")}</DropdownMenuLabel>

                {appOrAdminData && (
                  <>
                    <DropdownMenuGroup className="py-1">
                      {appOrAdminData.myTenants.map((tenant) => {
                        const isCurrentTenant = tenant.id == appOrAdminData.currentTenant?.id;
                        const tenantpathUrl = `/app/${tenant.slug}/dashboard`;
                        return (
                          <Link key={tenant.id} role="menuitem" to={tenantpathUrl} className={`gap-2`}>
                            <DropdownMenuItem
                              className={`hover:!bg-primary-light flex items-center justify-start gap-[12px] ${isCurrentTenant ? "text-primary" : ""} hover:!bg-primary-light hover:!text-primary py-2 text-xs`}
                            >
                              {tenant.icon ? (
                                <img className="size-4 shrink-0" src={tenant.icon} alt={tenant.name} />
                              ) : (
                                <span className="bg-primary border-primary-light flex size-4 items-center justify-center rounded-md border p-[7px] text-[6px] text-white uppercase">
                                  {tenant.name[0]}
                                </span>
                              )}

                              {tenant.name}
                            </DropdownMenuItem>
                          </Link>
                        );
                      })}
                    </DropdownMenuGroup>
                    <div className="border-sidebar-border mx-1 border-t"></div>

                    <Link to={"/new-account"} role="menuitem">
                      <DropdownMenuItem className="hover:!bg-primary-light hover:!text-primary flex items-center gap-2 py-2 text-[13px]">
                        <div className="flex size-4 items-center justify-center">
                          <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M14.9974 10.8385H10.8307V15.0052C10.8307 15.4635 10.4557 15.8385 9.9974 15.8385C9.53906 15.8385 9.16406 15.4635 9.16406 15.0052V10.8385H4.9974C4.53906 10.8385 4.16406 10.4635 4.16406 10.0052C4.16406 9.54688 4.53906 9.17188 4.9974 9.17188H9.16406V5.00521C9.16406 4.54688 9.53906 4.17188 9.9974 4.17188C10.4557 4.17188 10.8307 4.54688 10.8307 5.00521V9.17188H14.9974C15.4557 9.17188 15.8307 9.54688 15.8307 10.0052C15.8307 10.4635 15.4557 10.8385 14.9974 10.8385Z"
                              fill="currentColor"
                            />
                          </svg>
                        </div>
                        {t("app.tenants.create.title")}
                      </DropdownMenuItem>
                    </Link>
                  </>
                )}
              </Fragment>
            ) : layout === "admin" ? (
              <Fragment>
                <DropdownMenuGroup>
                  <Link to="/admin/settings/profile">
                    <DropdownMenuItem>
                      <GearIcon className="mr-2 h-4 w-4" />
                      {t("app.navbar.profile")}
                    </DropdownMenuItem>
                  </Link>
                </DropdownMenuGroup>
              </Fragment>
            ) : null}

            {DARK_MODE_IN_APP && (
              <DropdownMenuItem className="hover:!bg-primary-light hover:!text-primary gap-2 py-2 text-[13px]" onClick={onThemeToggle}>
                <DarkModeSvgs className="mr-2 size-4" withLabel={false} />
                {userSession?.lightOrDarkMode === "dark" ? "Light Mode" : "Dark Mode"}
              </DropdownMenuItem>
            )}
            <div className="border-sidebar-border mx-1 border-t"></div>

            <Link to="/logout">
              <DropdownMenuItem variant="destructive" className="!focus:bg-destructive !text-destructive py-2 text-[13px]">
                <LogOut className="mr-2 h-4 w-4" />
                {t("app.navbar.logout")}
              </DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
