import { use<PERSON><PERSON><PERSON>, use<PERSON>oader<PERSON><PERSON>, useParams } from "react-router";
import { Phone, LucideMail, PlusCircle } from "lucide-react";
import { JSX, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import FloatingLoader from "~/components/ui/loaders/FloatingLoader";
import ActivityPanel from "~/custom/components/ActivityPanel/ActivityPanel";
import DropdownMenu from "~/custom/components/tables/ThreeDotMenu";
import CircleStepper from "~/custom/components/CircleStepper/CircleStepper";
import OnboardingSessionBadge from "~/modules/onboarding/components/OnboardingSessionBadge";
import { Rows_Overview } from "~/modules/rows/routes/Rows_Overview.server";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import RewardsModal from "~/custom/components/modals/rewardModal";
import InputText from "~/components/ui/input/InputText";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
// import RewardsModal from "~/custom/components/modals/rewardModal";

interface ContactItemProps {
  icon: JSX.Element;
  content: string | JSX.Element;
}

const ContactItem = ({ icon, content }: ContactItemProps) => (
  <article className="flex shrink basis-0 items-center gap-2.5">
    {icon}
    <p className="my-auto overflow-hidden text-ellipsis whitespace-nowrap">{content}</p>
  </article>
);

interface EntityContactInfoState {
  phoneNumber?: string | JSX.Element;
  email?: string | JSX.Element;
}

interface EntityContactInfoProps {
  rowData: RowsApi.GetRowData;
  item: RowWithDetails;
  agent: any;
  options?: {
    hideActivity?: boolean;
  };
}

const EntityContactInfo = ({ rowData, item, agent, options }: EntityContactInfoProps) => {
  const [loading, setLoading] = useState(false);
  const [showRewards, setShowRewards] = useState(false);
  const [agentId, setAgentId] = useState<string | null>(null);
  // const { id } = useParams();
  const [showActivityPanel, setShowActivityPanel] = useState(false);
  const [showAddCoinsPanel, setShowAddCoinsPanel] = useState(false);
  const [coinsAmount, setCoinsAmount] = useState("");
  const [message, setMessage] = useState("");

  const { activityData } = useLoaderData<Rows_Overview.LoaderData>();
  const fetcher = useFetcher();

  const { t } = useTranslation();

  const [contactInfo, setContactInfo] = useState<EntityContactInfoState>({
    phoneNumber: "",
    email: "",
  });

  useEffect(() => {
    const getTitleFieldIds = (searchTerms: string[] = []) => {
      return rowData.entity.properties
        .filter(({ name, title }) => {
          return [name, title]
            .filter((s) => typeof s === "string")
            .map((s) => s.toLowerCase().trim())
            .some((s) => searchTerms.some((term) => s.includes(term)));
        })
        .map((o) => o.id);
    };

    const phoneNumberIds = getTitleFieldIds(["phone"]);
    const phoneNumberValues = item.values
      .filter((v) => phoneNumberIds.includes(v.propertyId))
      .map((o) => o.textValue || "")
      .filter((s) => !!s);

    const emailIds = getTitleFieldIds(["email"]);
    const emailValues = item.values
      .filter((v) => emailIds.includes(v.propertyId))
      .map((o) => o.textValue || "")
      .filter((s) => !!s);

    setContactInfo({
      email: emailValues?.length ? emailValues[0].trim() : "",
      phoneNumber: phoneNumberValues?.length ? phoneNumberValues[0].trim() : "",
    });
  }, [rowData?.entity, item, t]);

  const toggleActivityPanel = () => {
    setShowActivityPanel(!showActivityPanel);
  };

  const toggleAddCoinsPanel = () => {
    setShowAddCoinsPanel(!showAddCoinsPanel);
  };

  const handleAddCoins = () => {
    if (!coinsAmount || isNaN(Number(coinsAmount))) {
      return;
    }

    fetcher.submit(
      {
        action: "add-coins-by-row",
        coins: coinsAmount,
        message: message,
      },
      { method: "POST" }
    );

    setCoinsAmount("");
    setMessage("");
    setShowAddCoinsPanel(false);
  };

  return (
    <>
      <FloatingLoader loading={loading} />
      <section className="flex items-start justify-between gap-5 py-4 text-sm font-medium text-stone-800">

        <div className="flex flex-row gap-4">
          <div className="flex gap-2">
            {!options?.hideActivity && rowData?.entity?.name === "Agent" && (
              <>
                <ButtonSecondary
                  onClick={toggleAddCoinsPanel}
                  className="text-xs flex items-center gap-1"
                >
                  <PlusCircle className="h-4 w-4" />
                  Add Tokens
                </ButtonSecondary>
                <ButtonSecondary onClick={toggleActivityPanel} className="text-xs">
                  Activity
                </ButtonSecondary>
              </>
            )}
          </div>
          {/* <DropdownMenu item={item} setLoading={setLoading} entity={rowData?.entity} id={id ?? ""} flag={true} /> */}
        </div>
        <ActivityPanel isOpen={showActivityPanel} onClose={toggleActivityPanel} activityData={activityData ?? []} />

        {showAddCoinsPanel && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="relative w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
              {/* Close button - top right */}
              <button
                onClick={toggleAddCoinsPanel}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
                disabled={fetcher.state === "submitting"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                <span className="sr-only">Close</span>
              </button>

              <h3 className="text-lg font-medium text-gray-900">Tokens to Award</h3>
              <div className="mt-4">
                <InputText
                  type="number"
                  name="coins"
                  title="Add Tokens"
                  value={coinsAmount}
                  setValue={(e) => setCoinsAmount(e)}
                  placeholder="Number of Tokens to add"
                />
              </div>
              <div className="mt-4">
                <InputText name="message" title="Message" value={message} setValue={(e) => setMessage(e)} placeholder="Reason for adding tokens" rows={4} />
              </div>
              <div className="mt-6 flex justify-end gap-3">
                <ButtonSecondary onClick={toggleAddCoinsPanel} disabled={fetcher.state === "submitting"}>
                  Cancel
                </ButtonSecondary>
                <ButtonPrimary onClick={handleAddCoins} disabled={!coinsAmount || isNaN(Number(coinsAmount)) || fetcher.state === "submitting"}>
                  {fetcher.state === "submitting" ? "Adding..." : "Add Tokens"}
                </ButtonPrimary>
              </div>
            </div>
          </div>
        )}
      </section>

      {showRewards && agentId && <RewardsModal agentId={agentId} onClose={() => setShowRewards(false)} />}
    </>
  );
};

export default EntityContactInfo;