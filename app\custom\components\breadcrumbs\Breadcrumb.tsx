import clsx from "clsx";
import { Link, useLocation, useParams } from "react-router";
import { Fragment, useMemo } from "react";

interface BreadCrumbItem {
  icon?: JSX.Element | string;
  link: string;
  label: string;
}

const BreadCrumItem = ({ idx, item, isLast = false, className = "" }: { idx: number; className?: string; item: BreadCrumbItem; isLast?: boolean }) => {
  return (
    <div className="flex items-center gap-[5px] ">
      {/* {item.icon && typeof item.icon === "string" ? (
        <img loading="lazy" src={item.icon} className="mr-1 h-4 w-4 shrink-0 object-contain" alt={item.label} />
      ) : (
        item.icon && <span className="mr-1 flex h-4 w-4 items-center justify-center">{item.icon}</span>
      )} */}

      {isLast ? (
        <span className="text-[13px] font-semibold text-[#0B0A09] w-[80px]  truncate sm:w-auto">
          {item.label}
        </span>

      ) : (
        <>
          <Link to={item.link} className="hover:text-opacity-80 text-[13px] font-normal text-[#4D4D4D] hover:underline">
            {item.label}
          </Link>

          <span className="flex items-center px-[5px] !h-4 !w-4">
            <svg width="6" height="13" viewBox="0 0 6 9" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative top-[1px] h-[13px] w-[6px]">
              <path d="M1.5025 0.5L0.5625 1.44L3.61583 4.5L0.5625 7.56L1.5025 8.5L5.5025 4.5L1.5025 0.5Z" fill="#4D4D4D" />
            </svg>
          </span>
        </>
      )}
    </div>
  );
};

interface MenuItem {
  title: string;
  routePath?: string;
  icon?: JSX.Element | string;
}

interface Props {
  menu: MenuItem[];
  className?: string;
  home?: string;
}

export default function BreadcrumbCustom({ menu = [], className = "", home = "" }: Props) {
  const params = useParams();
  const location = useLocation();
  const isOverViewPage = params.entity && params.id && !location.pathname.includes("edit") && location.pathname.includes("app");
  const items = useMemo(
    () => [
      ...(home
        ? [
          {
            label: "Home",
            link: home.length ? home : "/",
            icon: undefined,
          },
        ]
        : []),
      ...menu.filter((o) => o.title).map((o) => ({ label: o.title, link: o.routePath || "/", icon: o.icon })),
      // ...(isOverViewPage && params.entity
      //   ? [
      //       {
      //         label: `${params.entity[0].toUpperCase() + params.entity.slice(1)} Details`,
      //         link: "",
      //         icon: undefined,
      //       },
      //     ]
      //   : []),
    ],
    [home, menu, params.entity]
  );

  return (
    <nav className={clsx("not-prose flex truncate", className)} aria-label="Breadcrumb">
      <ol className="flex flex-row  items-center gap-[5px] ">
        {items.map((item, index: number) => (
          <li key={item.label}>
            {/* {(index > 0 || home) && <RightIcon className="h-4 w-4 flex-shrink-0 text-gray-400" />} */}
            <BreadCrumItem idx={index} item={item} isLast={!(index < items.length - 1)} />
          </li>
        ))}
      </ol>
    </nav>
  );
}
