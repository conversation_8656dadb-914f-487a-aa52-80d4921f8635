"use client";

import * as React from "react";
import { ChevronsUpDown, Plus, SidebarIcon } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "~/components/ui/sidebar";
import { useTranslation } from "react-i18next";
import { useAppData } from "~/utils/data/useAppData";
import { data, Link, useLocation, useNavigate, useSearchParams } from "react-router";
import { TenantSimple } from "~/utils/db/tenants.db.server";
import clsx from "clsx";
import IconDashboard from "~/components/layouts/icons/IconDashboard";
import { cn } from "~/lib/utils";
import { useDropdown } from "~/components/Context/ToggleNavBar";
// import IconDashboard from "../

export function CustomTeamSwitcher({
  tenants,
  size = "md",
  groupsData,
  groupName,
}: {
  tenants: TenantSimple[];
  size?: "sm" | "md";
  groupsData?: any;
  groupName?: any;
}) {
  const { t } = useTranslation();
  const { isMobile, state, isHovering, setIsHovering } = useSidebar();
  const appData = useAppData();
  const location = useLocation();
  const navigate = useNavigate();
  const isCollapsed = state === "collapsed";
  const [activeTenant, setActiveTeam] = React.useState(appData?.currentTenant);
  const activeTenantDescription = appData.mySubscription?.products.length
    ? t(appData.mySubscription.products.find((f) => f)?.subscriptionProduct.title || "")
    : t("settings.subscription.noSubscription");

  const addClassToSvg = (svg: string, className: string): string => {
    return svg.replace(/<svg([\s\S]*?)>/, `<svg$1 class="${className}">`);
  };
  const [searchParams] = useSearchParams();
  const selectGroupName = searchParams.get("group") === "undefined" ? undefined : searchParams.get("group");

  const isMainDashboard = () => {
    if (location.pathname.includes("/dashboard")) {
      return true;
    }
    return !location.pathname.includes("/g/") && !searchParams.has("group");
  };

  const checkDashboard = () => {
    return location.pathname.endsWith("/dashboard");
  };

  const [isOpen, setIsOpen] = React.useState(false);
  const { isOpening } = useDropdown();
  if (isOpen && !isOpening) {
    setIsHovering(true);
  }

  return (
    <SidebarMenu className="">
      <SidebarMenuItem className={cn("border-border border-t border-b", isCollapsed && "flex justify-center py-[21.5px]")}>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild className="hover:!rounded-none active:!rounded-none">
            <SidebarMenuButton
              size={size === "md" ? "lg" : "default"}
              className="data-[state=open]:bg-accent data-[state=open]:text-foreground !h-[77px] !px-[14px] !py-4"
            // tooltip={isCollapsed ? (isMainDashboard() ? 'Dashboard' : groupName) : undefined}
            >
              <div
                className={clsx(
                  "flex aspect-square items-center justify-center rounded-lg",
                  size === "sm" && "size-6",
                  size === "md" && "text-foreground size-8"
                )}
              >
                {activeTenant.icon ? (
                  <img className="size-7 shrink-0 rounded-md" src={activeTenant.icon} alt={activeTenant.name} />
                ) : (
                  <span className="icon bg-primary-light inline-flex size-7 shrink-0 items-center justify-center rounded-md">
                    <span className="icon-text text-primary text-[13px] leading-none font-medium uppercase">
                      {isMainDashboard() && checkDashboard() ? "M" : selectGroupName?.charAt(0) || groupName?.charAt(0)}
                    </span>
                  </span>
                )}
              </div>
              <div className="grid flex-1 text-left text-[13px] leading-tight">
                <span className="!text-sidebar-foreground truncate font-semibold capitalize">
                  {isMainDashboard() && checkDashboard()
                    ? "Main Dashboard"
                    : location.pathname.includes(`/dashboard`)
                      ? "Main Dashboard"
                      : (selectGroupName ?? groupName)}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className={`mt-2 ml-1 w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg ${isCollapsed ? "mt-[-4] ml-4" : ""} ${isMobile ? "ml-[50%]" : ""} `}
            align="start"
            side={isMobile ? "top" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground py-2 text-[13px]">Apps</DropdownMenuLabel>
            {groupsData?.groups?.map((tenant: any, index: number) => (
              <DropdownMenuItem
                key={tenant.title}
                onClick={() => {
                  navigate(`/app/${groupsData?.currentTenant?.slug}/g/${tenant?.slug}`, {
                    state: { from: location.pathname },
                  });
                }}
                className={`hover:!bg-primary-light hover:!text-primary cursor-pointer gap-2 p-2 text-[13px] ${tenant?.slug === location.pathname.match(/\/g\/([^/]+)/)?.[1] ? "text-primary" : ""}`}
              >
                <div className="flex size-4 items-center justify-center rounded-sm">
                  {tenant?.icon?.startsWith?.("<svg") ? (
                    <span
                      className="size-[18px] shrink-0"
                      dangerouslySetInnerHTML={{
                        __html: addClassToSvg(tenant.icon, tenant?.slug === location.pathname.match(/\/g\/([^/]+)/)?.[1] ? "text-primary" : ""),
                      }}
                    />
                  ) : tenant?.icon ? (
                    <img className="text-primary size-4 shrink-0" src={tenant.icon} alt={tenant?.title} />
                  ) : (
                    <span className="inline-flex size-4 shrink-0 items-center justify-center rounded-md">
                      <span className="text-[13px] leading-none font-medium uppercase">{tenant?.name?.[0]}</span>
                    </span>
                  )}
                </div>

                {tenant?.title}
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator />
            <Link
              to={`/app/${appData?.currentTenant?.slug}/dashboard`}
              className={`${location.pathname === `/app/${appData?.currentTenant?.slug}/dashboard` ? "text-primary" : "inactive-option"}`}
            >
              <DropdownMenuItem className="hover:!bg-primary-light hover:!text-primary cursor-pointer gap-2 p-2 text-[13px]">
                <div>
                  <IconDashboard className={`${location.pathname === `/app/${appData?.currentTenant?.slug}/dashboard` ? "text-primary" : "inactive-option"}`} />
                </div>
                Main Dashboard
              </DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
