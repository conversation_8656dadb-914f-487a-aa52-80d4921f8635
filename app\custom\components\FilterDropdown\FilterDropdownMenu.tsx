// import { Input } from '@headlessui/react';
import { Transition } from "@headlessui/react";
import clsx from "clsx";
import { t } from "i18next";
import React, { FormEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Form, useSearchParams } from "react-router";
import { Colors } from "~/application/enums/shared/Colors";
import { Button } from "~/components/ui/button";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
// import { Form } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import InputCheckboxInline from "~/components/ui/input/InputCheckboxInline";
// import { FilterDto, FilterValueDto } from "~/components/ui/input/InputFilters";
import InputSearch from "~/components/ui/input/InputSearch";
import InputSelect from "~/components/ui/input/InputSelect";
import { useIsMobile } from "~/hooks/use-mobile";
import { cn } from "~/lib/utils";
import { updateItemByIdx } from "~/utils/shared/ObjectUtils";

export type FilterDto = {
  name: string;
  title: string;
  options?: { name: string; value: string; color?: Colors }[];
  hideSearch?: boolean;
  isBoolean?: boolean;
  fallbackValue?: string;
};

export type FilterValueDto = FilterDto & {
  value?: string;
};

interface Props {
  filters: FilterDto[];
  withSearch?: boolean;
  withName?: boolean;
  size?: "xs" | "sm" | "default" | "lg";
  position?: "left" | "right";
  handleFilter?: any;
}

const FilterDropdownMenu = ({ filters, withSearch = true, handleFilter }: Props) => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();

  // const [opened, setOpened] = useState(false);
  const [items, setItems] = useState<FilterValueDto[]>([]);
  const [filteredItems, setFilteredItems] = useState<number>(0);
  const [searchInput, setSearchInput] = useState("");
  const hasActiveItems = items.some(item => !!item.value);
  const hasActiveSearch = searchInput.trim() !== "";

  const isClearDisabled = !hasActiveItems && !hasActiveSearch;
  const isApplyDisabled = !hasActiveItems && !hasActiveSearch;

  useEffect(() => {
    if (filters) {
      const items: FilterValueDto[] = filters?.map((item) => {
        const value = searchParams.get(item.name) ?? undefined;
        return {
          ...item,
          value,
        };
      });
      setItems(items);
      setSearchInput(searchParams.get("q") ?? "");
    }
  }, [filters, searchParams]); // <-- make sure this includes `searchParams`

  useEffect(() => {
    const updated = { ...openAccordions };
    filters.forEach((f) => {
      if (!(f.name in updated)) {
        updated[f.name] = false;
      }
    });
    setOpenAccordions(updated);
  }, [filters]);

  useEffect(() => {
    const appliedFilters: FilterValueDto[] = [];
    items.forEach((item) => {
      const value = searchParams.get(item.name) ?? item.fallbackValue;
      if (value) {
        appliedFilters.push(item);
      }
    });
    if (withSearch) {
      setFilteredItems(appliedFilters.length + (searchParams.get("q") ? 1 : 0));
    } else {
      setFilteredItems(appliedFilters.length);
    }
  }, [items, searchInput, searchParams, withSearch]);

  function onClear() {
    // setOpened(false);

    items.forEach((item) => {
      searchParams.delete(item.name);
      item.selected = false;
      item.value = undefined;
    });
    setItems(items);

    searchParams.delete("page");
    searchParams.delete("q");
    setSearchInput("");

    setSearchParams(searchParams);
  }

  useEffect(() => {
    // Whenever filters change, ensure there's an entry for each filter
    const newOpenAccordions: Record<string, boolean> = {};
    filters.forEach((filter) => {
      newOpenAccordions[filter.name] = openAccordions[filter.name] ?? false;
    });
    setOpenAccordions(newOpenAccordions);
  }, [filters]);

  function onSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    items.forEach((item) => {
      if (item.value) {
        searchParams.set(item.name, item.value.toString());
      } else {
        searchParams.delete(item.name);
      }
    });
    if (searchInput) {
      searchParams.set("q", searchInput);
    } else {
      searchParams.delete("q");
    }
    searchParams.delete("page");
    setSearchParams(searchParams);
    // setOpened(false);
  }

  // Add this hook once:
  const [openAccordions, setOpenAccordions] = useState<Record<string, boolean>>({});
  const isMobile = useIsMobile();
  return (
    <div className={` border-none ring-0 outline-none ${isMobile ? '' : 'mb-[23px]'}`}>
      <Form
        style={{ boxShadow: "0px 12px 24px -4px rgba(145, 158, 171, 0.12)" }}
        onSubmit={onSubmit}
        method="get"
        className={cn(
          "bg-white divide-border ring-opacity-5 z-40  flex min-h-full  flex-col justify-between  rounded-md border-1 !border-[#E6E6E6] ring-0 outline-none focus:border-none focus:ring-0 focus:outline-none",
          isMobile ? 'w-full' : 'w-[282px] my-3 ml-3'
          //   position === "right" ? "right-0" : "left-0"
        )}
      >
        <div className="flex items-center justify-between px-3 py-4">
          <div className="text-sm font-bold text-[#262626]">{t("shared.filters")}</div>
          <ButtonSecondary
            type="button"
            onClick={handleFilter}
            className="mr-2 h-6 px-2 rounded-sm cursor-pointer hover:bg-gray-100 transition border-1"
          >
            Close
          </ButtonSecondary>

        </div>

        <div className="bg-background rounded-md text-sm">
          {items.map((filter, idx) => {
            const isOpen = openAccordions[filter.name] ?? false;
            return (
              <div key={filter.name} className="border-b border-[#E6E6E6] last:border-b-0">
                {/* Accordion Header */}
                <div
                  className="flex cursor-pointer items-center justify-between px-3 py-2"
                  onClick={() =>
                    setOpenAccordions((prev) => ({
                      ...prev,
                      [filter.name]: !isOpen,
                    }))
                  }
                >
                  <span className="font-medium text-[#262626]">{filter.title.includes(".") ? t(filter.title) : filter.title}</span>
                  <span
                    className={clsx("transition-all duration-300 ease-in-out", isOpen ? "rotate-180" : "rotate-0", "flex h-4 w-4 items-center justify-center")}
                  >
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M7 10L12 15L17 10" stroke="#000" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </span>
                </div>

                <Transition
                  show={isOpen}
                  enter="transition-all ease-[cubic-bezier(0.4,0,0.2,1)] duration-200"
                  enterFrom="transform opacity-0 scale-y-90"
                  enterTo="transform opacity-100 scale-y-100"
                  leave="transition-all ease-[cubic-bezier(0.4,0,0.2,1)] duration-200"
                  leaveFrom="transform opacity-100 scale-y-100"
                  leaveTo="transform opacity-0 scale-y-90"
                >
                  <div className="bg-secondary overflow-hidden px-2 py-3">
                    {filter.options && filter.options.length > 0 ? (
                      <div className="flex flex-wrap items-center gap-2">
                        {filter.options.map((option) => (
                          <ButtonSecondary
                            key={option.value}
                            type="button"
                            onClick={() =>
                              updateItemByIdx(items, setItems, idx, {
                                value: filter.value === option.value ? undefined : option.value,
                              })
                            }
                            className={clsx(
                              "!h-[22px] cursor-pointer rounded-sm border px-2 py-1 text-[13px]",
                              filter.value === option.value
                                ? "border-[#202229] bg-[#202229] font-semibold text-white hover:bg-[#202229]"
                                : "border-gray-300 bg-white text-gray-700 hover:bg-[#F2F2F2]"
                            )}
                          >
                            {option.name && option.name.includes(".") ? t(option.name) : option.name}
                          </ButtonSecondary>
                        ))}
                      </div>
                    ) : filter.isBoolean ? (
                      <div className="flex items-center space-x-2">
                        <InputSelect
                          name={filter.name}
                          title=""
                          placeholder={t("shared.select") + "..."}
                          options={[
                            { name: t("shared.yes"), value: "true" },
                            { name: t("shared.no"), value: "false" },
                          ]}
                          value={filter.value ?? filter.fallbackValue ?? ""}
                          withLabel={false}
                          setValue={(e) => {
                            updateItemByIdx(items, setItems, idx, {
                              value: e,
                            });
                          }}
                          className="bg-background w-full pb-1"
                        />
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Input
                          type="text"
                          name={filter.name}
                          autoComplete="off"
                          className="focus:border-border bg-background border-border block w-full min-w-0 flex-1 rounded-md p-1 text-sm"
                          required
                          value={filter.value ?? filter.fallbackValue ?? ""}
                          onChange={(e) => {
                            updateItemByIdx(items, setItems, idx, {
                              value: e.currentTarget.value,
                            });
                          }}
                        />
                      </div>
                    )}
                  </div>
                </Transition>
              </div>
            );
          })}
        </div>
        {!isMobile && <div className="mt-auto flex w-full items-center justify-between border-t border-[#E6E6E6] px-2 py-2 text-sm">
          <Button
            type="button"
            variant="outline"
            onClick={onClear}
            disabled={isClearDisabled}
            className="cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
          >
            Clear All
          </Button>

          <Button
            type="submit"
            variant="outline"
            disabled={isApplyDisabled}
            className="bg-[#274AFF] text-white hover:bg-[#274AFF]/90 hover:text-white cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
          >
            {t("shared.apply")}
          </Button>
        </div>}

        {isMobile && (
          <div className="fixed bottom-0 left-0 w-full z-[120] bg-white border-t border-[#E6E6E6] px-2 py-2 text-sm">
            <div className="flex w-full items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={onClear}
                disabled={isClearDisabled}
                className="cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
              >
                Clear All
              </Button>

              <Button
                type="submit"
                variant="outline"
                disabled={isApplyDisabled}
                className="bg-[#274AFF] text-white hover:bg-[#274AFF]/90 hover:text-white cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
              >
                {t("shared.apply")}
              </Button>
            </div>
          </div>
        )}


      </Form>
    </div>
  );
};

export default FilterDropdownMenu;