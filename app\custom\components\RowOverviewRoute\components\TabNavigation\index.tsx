'use client';
import clsx from "clsx";
import { useEffect, useState } from "react";
import { useLocation, useParams } from "react-router";
import { useIsMobile } from "~/hooks/use-mobile";

interface Tab {
  name: string;
  href?: string;
  current?: boolean;
  count?: number;
}

interface TabNavigationProps {
  tabs?: Tab[];
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  className?: string;
}

const TabNavigation = ({
  tabs = [
    { name: "Details", current: false },
    { name: "Job Applications", current: false },
    { name: "Contract", current: false },
  ],
  activeTab,
  onTabChange,
  className = 'h-[56px]'
}: TabNavigationProps) => {
  const isMobile = useIsMobile();
  const params = useParams()
  const location = useLocation();
  const isOverViewPage = params?.id && location?.pathname.includes("app") && !location?.pathname.includes("edit");



  return (
    <nav

      className={clsx(
        // "flex min-h-12 w-full flex-wrap items-start gap-[12px] sm:gap-8 border-b border-zinc-100 pl-4 text-xs leading-6 text-black text-opacity-60 max-md:max-w-full lg:gap-8 !bg-white",
        "sticky top-5 z-10 flex min-h-12    items-start gap-[12px] sm:gap-8  pl-4 text-xs leading-6 text-black text-opacity-60 max-md:max-w-full lg:gap-8 bg-white  mb-3 md:mb-0 md:mx-3  overflow-x-auto",
        isMobile ? 'px-2' : '',
        className
      )}
    >
      {tabs.map((tab) => {
        const isActive = tab.current ?? tab.name === activeTab;

        return (
          <a
            key={tab.name}
            href={tab.href || '#'}
            onClick={(e) => {
              if (onTabChange) {
                e.preventDefault();
                onTabChange(tab.name);
              }
            }}
            className={clsx(
              "group flex min-h-12 items-center gap-3 self-stretch py-4 transition-colors duration-200",
              isActive
                ? "whitespace-nowrap border-b-2 border-primary font-semibold text-primary"
                : "text-zinc-500 hover:text-zinc-700"
            )}
            aria-current={isActive ? "page" : undefined}
          >
            <div className="flex items-center">
              {tab.name === "Activity Log" && (
                <div className="pr-1">
                  <svg width="16" height="16" viewBox="0 0 16 16" className="fill-current text-inherit" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 2C5.68667 2 3 4.68667 3 8H1L3.59333 10.5933L3.64 10.6867L6.33333 8H4.33333C4.33333 5.42 6.42 3.33333 9 3.33333C11.58 3.33333 13.6667 5.42 13.6667 8C13.6667 10.58 11.58 12.6667 9 12.6667C7.71333 12.6667 6.54667 12.14 5.70667 11.2933L4.76 12.24C5.84667 13.3267 7.34 14 9 14C12.3133 14 15 11.3133 15 8C15 4.68667 12.3133 2 9 2ZM8.33333 5.33333V8.66667L11.1667 10.3467L11.68 9.49333L9.33333 8.1V5.33333H8.33333Z" />
                  </svg>
                </div>
              )}
              <div className="flex flex-row items-center whitespace-nowrap">
                <span>{tab.name}</span>
                {/* <span className="ml-2">
                  {'count' in tab ? 
                  (<span className="inline-flex justify-center items-center px-1.5 py-0 rounded-md bg-zinc-100 hover:bg-[#FFF3D6] h-[22px] min-w-[22px] text-xs leading-6 text-neutral-900 font-semibold">
                    {typeof tab.count === 'number' ? tab.count : 0}
                  </span>): null}
                </span> */}
                <span className="ml-2">
                  {'count' in tab ?
                    (<span className={clsx(
                      "inline-flex justify-center items-center px-1.5 py-0 rounded-md h-[22px] min-w-[22px] text-xs leading-6 font-semibold",
                      isActive
                        ? "bg-primary-light text-primary"
                        : "bg-accent  text-[#202229]"
                    )}>
                      {typeof tab.count === 'number' ? tab.count : 0}
                    </span>)
                    : null}
                </span>
              </div>
            </div>
          </a>
        );
      })}
    </nav>
  );
};

export default TabNavigation;
