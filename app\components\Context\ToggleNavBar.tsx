import React, { createContext, useContext, useState } from "react";

type ToggleType = {
  isOpening: boolean;
  setIsOpening: React.Dispatch<React.SetStateAction<boolean>>;
};

const ToggleContext = createContext<ToggleType | undefined>(undefined);

export function ToggleProvider({ children }: { children: React.ReactNode }) {
  const [isOpening, setIsOpening] = useState(false);

  return <ToggleContext.Provider value={{ isOpening, setIsOpening }}>{children}</ToggleContext.Provider>;
}

export function useDropdown() {
  const context = useContext(ToggleContext);
  if (!context) {
    throw new Error("useDropdown must be used within a DropdownProvider");
  }
  return context;
}
