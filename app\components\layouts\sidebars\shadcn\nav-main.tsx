"use client";

import { ChevronRight } from "lucide-react";
import { Fragment } from "react/jsx-runtime";
import { SidebarGroupDto } from "~/application/sidebar/SidebarGroupDto";

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "~/components/ui/sidebar";
import SidebarIcon from "../../icons/SidebarIcon";
import { Link, useLocation, useParams } from "react-router";
// import { useKBar } from "kbar";
import { cn } from "~/lib/utils";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import clsx from "clsx";
import React from "react";

function isDashboardItem(item: any) {
  return item?.title === "Dashboard";
}

export function NavMain({
  items,
  isAdmin,
  
}: {
  // items: {
  //   title: string;
  //   url: string;
  //   icon: LucideIcon;
  //   isActive?: boolean;
  //   items?: {
  //     title: string;
  //     url: string;
  //   }[];
  // }[];
  items: SidebarGroupDto[];
  isAdmin?: boolean;
}) {
  const location = useLocation();
  // const { query } = useKBar();
  // function onOpenCommandPalette() {
  //   query.toggle();
  // }
  const isAdminRoute = location.pathname.startsWith("/admin");
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";
  const appOrAdminData = useAppOrAdminData();
  const params = useParams();
  const agentRoleExists = appOrAdminData.isSuperAdmin;
  const modifiedItems = [...items];
  
  if (params.group === "loyality" && agentRoleExists && modifiedItems.length > 0) {
    const firstGroup = { ...modifiedItems[0] };
    const alreadyAdded = firstGroup.items.some((item) => item.title === "Rewards" || item.title === "Milestones");
    if (!alreadyAdded) {
      firstGroup.items = [
        ...firstGroup.items,
        {
          title: "Rewards",
          path: `/app/${params.tenant}/g/${params.group}/rewards`,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 25" fill="none">
              <g clipPath="url(#clip0_5933_59174)">
                <path
                  d="M20 6.3125H17.82C17.93 6.0025 18 5.6625 18 5.3125C18 3.6525 16.66 2.3125 15 2.3125C13.95 2.3125 13.04 2.8525 12.5 3.6625L12 4.3325L11.5 3.6525C10.96 2.8525 10.05 2.3125 9 2.3125C7.34 2.3125 6 3.6525 6 5.3125C6 5.6625 6.07 6.0025 6.18 6.3125H4C2.89 6.3125 2.01 7.2025 2.01 8.3125L2 19.3125C2 20.4225 2.89 21.3125 4 21.3125H20C21.11 21.3125 22 20.4225 22 19.3125V8.3125C22 7.2025 21.11 6.3125 20 6.3125ZM15 4.3125C15.55 4.3125 16 4.7625 16 5.3125C16 5.8625 15.55 6.3125 15 6.3125C14.45 6.3125 14 5.8625 14 5.3125C14 4.7625 14.45 4.3125 15 4.3125ZM9 4.3125C9.55 4.3125 10 4.7625 10 5.3125C10 5.8625 9.55 6.3125 9 6.3125C8.45 6.3125 8 5.8625 8 5.3125C8 4.7625 8.45 4.3125 9 4.3125ZM20 19.3125H4V17.3125H20V19.3125ZM20 14.3125H4V9.3125C4 8.7625 4.45 8.3125 5 8.3125H9.08L7.6 10.3325C7.27 10.7825 7.37 11.4125 7.82 11.7325C8.26 12.0525 8.89 11.9525 9.21 11.5125L12 7.7125L14.79 11.5125C15.11 11.9525 15.74 12.0525 16.18 11.7325C16.63 11.4125 16.73 10.7825 16.4 10.3325L14.92 8.3125H19C19.55 8.3125 20 8.7625 20 9.3125V14.3125Z"
                  fill="#0A0501"
                />
              </g>
              <defs>
                <clipPath id="clip0_5933_59174">
                  <rect width="24" height="24" fill="white" transform="translate(0 0.3125)" />
                </clipPath>
              </defs>
            </svg>
          ),
        },
        {
          title: "Milestones",
          path: `/app/${params.tenant}/g/${params.group}/milestones`,
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 25" fill="none">
              <g clipPath="url(#clip0_5933_59188)">
                <path
                  d="M23 12.3125L20.56 9.5225L20.9 5.8325L17.29 5.0125L15.4 1.8125L12 3.2725L8.6 1.8125L6.71 5.0025L3.1 5.8125L3.44 9.5125L1 12.3125L3.44 15.1025L3.1 18.8025L6.71 19.6225L8.6 22.8125L12 21.3425L15.4 22.8025L17.29 19.6125L20.9 18.7925L20.56 15.1025L23 12.3125ZM9.38 16.3225L7 13.9225C6.61 13.5325 6.61 12.9025 7 12.5125L7.07 12.4425C7.46 12.0525 8.1 12.0525 8.49 12.4425L10.1 14.0625L15.25 8.9025C15.64 8.5125 16.28 8.5125 16.67 8.9025L16.74 8.9725C17.13 9.3625 17.13 9.9925 16.74 10.3825L10.82 16.3225C10.41 16.7125 9.78 16.7125 9.38 16.3225Z"
                  fill="#0A0501"
                />
              </g>
              <defs>
                <clipPath id="clip0_5933_59188">
                  <rect width="24" height="24" fill="white" transform="translate(0 0.3125)" />
                </clipPath>
              </defs>
            </svg>
          ),
        },
      ];
      modifiedItems[0] = firstGroup;
    }
  }

  return (
    <Fragment>
      {isAdminRoute ? (
        <Fragment>
          {items.map((group, idxGroup) => (
            <SidebarGroup key={idxGroup}>
              {group.title && <SidebarGroupLabel className={cn({ "justify-center": isCollapsed })}>{group.title}</SidebarGroupLabel>}
              <SidebarMenu>
                {group.items.map((item, idx) => (
                  <Fragment key={idx}>
                    {!item.items?.length ? (
                      <SidebarMenuItem>
                        {location?.pathname === item?.path && (
                          <div className="absolute left-[-4px] top-0 h-full w-0.5 bg-primary z-10" />
                        )}
                        <SidebarMenuButton asChild>
                          <Link
                            to={item?.path}
                            className={`${(location?.pathname === item?.path ||
                                (location?.pathname.includes(item?.path) && item?.path !== "/"))
                                ? "bg-primary-light !text-primary hover:!bg-primary-light hover:!text-primary"
                                : ""
                              }`}
                          >
                           {(item.icon !== undefined || item.entityIcon !== undefined) && <SidebarIcon className="size-4" item={item} />}
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ) : (
                      <Collapsible asChild defaultOpen={item.isActive} className="group/collapsible">
                        <SidebarMenuItem>
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton >
                              {(item.icon !== undefined || item.entityIcon !== undefined) && <SidebarIcon className="size-4" item={item} />}
                              <span className="whitespace-nowrap transition-none">{item.title}</span>
                              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          {item.items?.length ? (
                            <>

                              <CollapsibleContent>
                                <SidebarMenuSub>
                                  {item.items?.map((subItem) => (
                                    <SidebarMenuSubItem key={subItem.title}>
                                      <SidebarMenuSubButton asChild>
                                        <Link to={subItem.path}>
                                          <span>{subItem.title}</span>
                                        </Link>
                                      </SidebarMenuSubButton>
                                    </SidebarMenuSubItem>
                                  ))}
                                </SidebarMenuSub>
                              </CollapsibleContent>
                            </>
                          ) : null}
                        </SidebarMenuItem>
                      </Collapsible>
                    )}
                  </Fragment>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          ))}
        </Fragment>
      ) : (
        <Fragment>
          {Array.isArray(items) &&
            modifiedItems?.map((group, idxGroup) => (
              <SidebarGroup
                key={idxGroup}
                // onMouseEnter={handleMouseEnter}
                className="relative"
              >
                {!isAdminRoute && <SidebarGroupLabel className={cn({ "justify-center": isCollapsed })}>Actions</SidebarGroupLabel>}
                <SidebarMenu>
                  {group?.items?.map((item, idx) => (
                    <Fragment key={idx}>
                      {!item.items?.length ? (
                        <SidebarMenuItem>
                          {location?.pathname === item?.path && (
                            <div className="absolute left-[-4px] top-0 h-full w-0.5 bg-primary z-10" />
                          )}
                          <SidebarMenuButton asChild>
                            <Link
                              to={item?.path}
                              className={`${(location?.pathname === item?.path ||
                                  (location?.pathname.includes(item?.path) &&
                                    item?.path !== "/" &&
                                    (!isDashboardItem(item) || location?.pathname.includes("/dashboard"))))
                                  ? "bg-primary-light !text-primary hover:!bg-primary-light hover:!text-primary"
                                  : ""
                                }`}
                            >
                              {(item?.icon !== undefined || item?.entityIcon !== undefined) &&
                                <SidebarIcon className="size-[18px]" item={item} />}
                              <span className="text-[13px]">{item?.title}</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ) : (
                        <Collapsible asChild defaultOpen={item.isActive} className="group/collapsible">
                          <SidebarMenuItem>
                            <CollapsibleTrigger asChild>
                              <SidebarMenuButton tooltip={item?.title}>
                                {/* <Link to={item.path}> */}
                                {/* <item.icon /> */}
                                {(item?.icon !== undefined || item?.entityIcon !== undefined) && <SidebarIcon className="size-4" item={item} />}
                                <span>{item?.title}</span>
                                <ChevronRight className="ml-auto transition-transform duration-200" />

                                {/* </Link> */}
                              </SidebarMenuButton>
                            </CollapsibleTrigger>
                            {item?.items?.length ? (
                              <>
                                {/* <CollapsibleTrigger asChild>
                            <SidebarMenuAction className="data-[state=open]:rotate-90"> */}
                                {/* <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                          <span className="sr-only">Toggle</span> */}
                                {/* </SidebarMenuAction>
                          </CollapsibleTrigger> */}
                                <CollapsibleContent>
                                  <SidebarMenuSub>
                                    {item?.items?.map((subItem) => (
                                      <SidebarMenuSubItem key={subItem?.title}>
                                        <SidebarMenuSubButton asChild size="sm">
                                          <Link to={subItem?.path}>
                                            <span className="text-foreground text-xs">{subItem?.title}</span>
                                          </Link>
                                        </SidebarMenuSubButton>
                                      </SidebarMenuSubItem>
                                    ))}
                                  </SidebarMenuSub>
                                </CollapsibleContent>
                              </>
                            ) : null}
                          </SidebarMenuItem>
                        </Collapsible>
                      )}
                    </Fragment>
                  ))}
                </SidebarMenu>
              </SidebarGroup>
            ))}
        </Fragment>
      )}
    </Fragment>
  );
}
