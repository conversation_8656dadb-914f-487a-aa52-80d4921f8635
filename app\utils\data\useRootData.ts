import { useMatches } from "react-router";
import { AppConfiguration } from "../db/appConfiguration.db.server";
import { UserSession } from "../session.server";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { ImpersonatingSessionDto } from "~/application/dtos/session/ImpersonatingSessionDto";
import { AnalyticsInfoDto } from "~/application/dtos/marketing/AnalyticsInfoDto";
import { UserWithoutPassword } from "../db/users.db.server";
import { TenantSimple } from "../db/tenants.db.server";

export type AppRootData = {
  metatags: MetaTagsDto;
  user: UserWithoutPassword | null;
  currentTenant: TenantSimple | null;
  theme: string;
  locale: string;
  serverUrl: string;
  domainName: string;
  userSession: UserSession;
  authenticated: boolean;
  debug: boolean;
  isStripeTest: boolean;
  chatWebsiteId?: string;
  appConfiguration: AppConfiguration;
  csrf?: string;
  analytics?: AnalyticsInfoDto;
  featureFlags: string[];
  impersonatingSession: ImpersonatingSessionDto | null;
};

export function useRootData(): AppRootData {
  const data = (useMatches().find((f) => f.pathname === "/" || f.pathname === "")?.data ?? {}) as AppRootData;

  // Simple logs to check userSession
  console.log("useRootData: data exists =", !!data);
  console.log("useRootData: userSession exists =", !!data.userSession);
  console.log("useRootData: userSession.cookies exists =", !!data.userSession?.cookies);

  if (!data.userSession) {
    console.warn("useRootData: userSession is undefined");
  } else if (!data.userSession.cookies) {
    console.warn("useRootData: userSession.cookies is undefined");
  } else {
    console.log("useRootData: userSession and cookies are loaded properly");
  }

  return data;
}
