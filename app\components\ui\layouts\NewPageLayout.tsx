
import { ReactNode } from "react";
import BreadcrumbSimple from "../breadcrumbs/BreadcrumbSimple";
import { cn } from "~/lib/utils";
import Stepper from "~/custom/components/CreatePageStepper";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import { useLocation, useSearchParams, useNavigate } from "react-router";

interface Props {
  title: string;
  menu?: {
    title: string;
    routePath?: string;
  }[];
  buttons?: ReactNode;
  children: ReactNode;
  className?: string;
  entity?: any;
  item?: RowWithDetails;
}
export default function NewPageLayout({ title, menu, buttons, children, className, entity, item }: Props) {
  const parseStep = (step: any) => {
    try {
      return JSON.parse(step.block);
    } catch {
      return null;
    }
  };

  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const steps = entity?.isOnboarding && entity?.onboarding ? entity?.onboarding.steps.map(parseStep).filter(Boolean) : [];
  const currentStepIndexStr = searchParams.get("step") ?? "0";
  const currentStepIndex = Number(currentStepIndexStr);
  const currentStep = steps[currentStepIndex];
  const onboardingSession = item?.onboardingSession ?? null;

  const location = useLocation();
  const isAdminPage = location.pathname.includes("admin");

  const handleCurrentStep = (stepIndex: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("step", stepIndex.toString());
    params.set("creating", "true"); // Always set this during creation
    navigate(`${location.pathname}?${params.toString()}`);
  };

  return (
    <div className={cn("", entity?.isOnboarding ? " flex-col " : "")}>
      <div className="sticky top-0 z-30 !bg-secondary ">
      {!isAdminPage && (
        
        <><div className=" text-[18px] font-bold text-[#202229] flex w-full items-center !justify-between px-4 max-w-[943px] sm:px-6 lg:px-8 mx-auto pb-[24px] pt-[24px] 
        ">
        {location?.pathname?.includes("new") ? `Add New ${entity?.name}` : `Edit ${entity?.name}`}
      </div>
        <div className={cn(
          "  flex w-full items-center !justify-between px-4 max-w-[943px] sm:px-6 lg:px-8  mx-auto  ",
          entity?.isOnboarding ? "" : ""
        )}>
          {entity?.isOnboarding ? (
            <>
                
                   <div
              style={{ boxShadow: '0px 4px 14.9px 0px #B7B7B740' }}
               className="flex w-full justify-center sticky top-0 z-50 bg-card rounded-[8px] h-[57px] p-4 border-[1px] border-input ">
                <Stepper steps={steps} currentStep={currentStepIndex} handleCurrentStep={handleCurrentStep} />
              </div>
              </>

          ) : (
           <></>
          )}
        </div>
        </>)}
        </div>

      <div className={entity?.isOnboarding ? "flex-1 overflow-y-auto" : ""}>
        <div className={cn(className, "mx-auto max-w-[943px] space-y-3 px-4 pt-4 pb-6 sm:px-6 lg:px-8")}>
          <div className="space-y-1">
            <div className="flex items-center justify-between space-x-2">
              {isAdminPage && <h1 className="flex flex-1 items-center truncate text-xl font-medium">{title}</h1>}
              <div className="flex items-center space-x-2">{buttons}</div>
            </div>

            {/* {menu && <BreadcrumbSimple menu={menu} />} */}
          </div>

          {children}
        </div>
      </div>
    </div>
  );
}
