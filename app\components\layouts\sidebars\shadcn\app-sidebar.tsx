"use client";

import * as React from "react";
import { AudioWaveform, BookOpen, Bot, Command, Frame, GalleryVerticalEnd, LifeBuoy, Map, PieChart, Send, Settings2, SquareTerminal } from "lucide-react";
import { NavMain } from "./nav-main";
import { NavQuickLinks } from "./nav-quick-links";
import { NavSecondary } from "./nav-secondary";
import { NavUser } from "./nav-user";
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "~/components/ui/sidebar";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { AppSidebar } from "~/application/sidebar/AppSidebar";
import { useTranslation } from "react-i18next";
import { useRootData } from "~/utils/data/useRootData";
import { SideBarItem } from "~/application/sidebar/SidebarItem";
import { AdminSidebar } from "~/application/sidebar/AdminSidebar";
import { Link, useLocation, useParams, useSearchParams } from "react-router";
import { DocsSidebar } from "~/application/sidebar/DocsSidebar";
import UrlUtils from "~/utils/app/UrlUtils";
import { useAppData } from "~/utils/data/useAppData";
import { SidebarGroupDto } from "~/application/sidebar/SidebarGroupDto";
import { TeamSwitcher } from "./team-switcher";
import NewTenantSelector from "../../selectors/NewTenantSelector";
import { DARK_SIDEBAR_IN_LIGHT_MODE } from "~/application/Constants";
import { cn } from "~/lib/utils";
import { Separator } from "~/components/ui/separator";
import entities from "~/routes/admin/entities/index";
import { sl } from "date-fns/locale";
import SearchButton from "../../buttons/SearchButton";
import { useKBar } from "kbar";
import AddFeedbackButton from "../../buttons/AddFeedbackButton";
import { CustomTeamSwitcher } from "~/custom/components/customTeamSwitcher";
import { Tooltip, TooltipTrigger, TooltipContent } from "~/components/ui/tooltip";
import Icon from "~/components/brand/Icon";
import Logo from "~/components/brand/Logo";

export function ShadcnAppSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  layout: "app" | "admin" | "docs";
  items?: SideBarItem[];
}) {
  const { t } = useTranslation();
  const location = useLocation();
  const rootData = useRootData();
  const appOrAdminData = useAppOrAdminData();
  const appData = useAppData();
  const params = useParams();
  const appConfiguration = rootData.appConfiguration;
  const [isAdmin, setIsAdmin] = React.useState(false);
  const [searchParams] = useSearchParams();
  const selectGroupName = searchParams.get("group");
  const { query } = useKBar();
  const { state, isHovering, setIsHovering } = useSidebar();
  const isCollapsed = state === "collapsed" && !isHovering;
  const groupsData = [
    {
      currentTenant: {
        id: appData?.currentTenant?.id,
        slug: appData?.currentTenant?.slug,
      },
      groups:
        appData?.entityGroups?.map((group) => ({
          title: group?.title,
          icon: group?.icon,
          slug: group?.slug,
          entities:
            group?.entities?.map((e: { entity: { icon: string; titlePlural: string; slug: string } }) => {
              const { icon, titlePlural, slug } = e.entity;
              return {
                entityIcon: icon,
                titlePlural,
                path: `/app/${appData?.currentTenant?.slug}/g/${group?.slug}/${slug}`,
              };
            }) || [],
        })) || [],
    },
  ];

  // const groupName = location?.pathname?.split("/g/")[1]?.split("/")[0] || selectGroupName ||   groupsData[0]?.groups?.[0]?.title ;

  const groupName = location?.pathname?.split("/g/")[1]?.split("/")[0] || selectGroupName || groupsData[0]?.groups?.[0]?.title;

  function findExactRoute(item: SideBarItem, pathname: string) {
    if (item.exact) {
      return UrlUtils.stripTrailingSlash(pathname) === UrlUtils.stripTrailingSlash(item.path);
    } else {
      return pathname.includes(item.path);
    }
  }

  function isMainDashboard() {
    const pathParts = location.pathname.split("/");
    return (
      location.pathname.includes(`/dashboard`) ||
      (pathParts.length >= 4 &&
        //  pathParts[1] === 'app'
        pathParts[3] !== "g" &&
        //  pathParts[3] !== 'settings' &&
        !pathParts.includes("dashboard"))
    );
  }

  // Function to get entities that are not in any group
  function getUngroupedEntities() {
    if (!appOrAdminData?.entities || !appData?.entityGroups) {
      return [];
    }

    // Get all entity IDs that are in groups
    const groupedEntityIds = new Set();
    appData.entityGroups.forEach((group) => {
      group.entities?.forEach((entityGroup) => {
        groupedEntityIds.add(entityGroup.entity.id);
      });
    });

    // Filter entities that are not in any group and should show in sidebar
    return appOrAdminData.entities
      .filter((entity) => !groupedEntityIds.has(entity.id) && entity.active && entity.showInSidebar)
      .map((entity) => ({
        title: entity.titlePlural,
        path: `/app/${appData?.currentTenant?.slug}/${entity.slug}`,
        entityIcon: entity.icon,
        items: [],
      }));
  }

  // const data = {
  //   // user: {
  //   //   name: "shadcn",
  //   //   email: "<EMAIL>",
  //   //   avatar: "/avatars/shadcn.jpg",
  //   // },
  //   navMain: [
  //     {
  //       title: "Playground",
  //       url: "#",
  //       icon: SquareTerminal,
  //       isActive: true,
  //       items: [
  //         {
  //           title: "History",
  //           url: "#",
  //         },
  //         {
  //           title: "Starred",
  //           url: "#",
  //         },
  //         {
  //           title: "Settings",
  //           url: "#",
  //         },
  //       ],
  //     },
  //     {
  //       title: "Models",
  //       url: "#",
  //       icon: Bot,
  //       items: [
  //         {
  //           title: "Genesis",
  //           url: "#",
  //         },
  //         {
  //           title: "Explorer",
  //           url: "#",
  //         },
  //         {
  //           title: "Quantum",
  //           url: "#",
  //         },
  //       ],
  //     },
  //     {
  //       title: "Documentation",
  //       url: "#",
  //       icon: BookOpen,
  //       items: [
  //         {
  //           title: "Introduction",
  //           url: "#",
  //         },
  //         {
  //           title: "Get Started",
  //           url: "#",
  //         },
  //         {
  //           title: "Tutorials",
  //           url: "#",
  //         },
  //         {
  //           title: "Changelog",
  //           url: "#",
  //         },
  //       ],
  //     },
  //     {
  //       title: "Settings",
  //       url: "#",
  //       icon: Settings2,
  //       items: [
  //         {
  //           title: "General",
  //           url: "#",
  //         },
  //         {
  //           title: "Team",
  //           url: "#",
  //         },
  //         {
  //           title: "Billing",
  //           url: "#",
  //         },
  //         {
  //           title: "Limits",
  //           url: "#",
  //         },
  //       ],
  //     },
  //   ],
  //   navSecondary: [
  //     {
  //       title: "Support",
  //       url: "#",
  //       icon: LifeBuoy,
  //     },
  //     {
  //       title: "Feedback",
  //       url: "#",
  //       icon: Send,
  //     },
  //   ],
  //   projects: [
  //     {
  //       name: "Quick link 1",
  //       url: "#",
  //       icon: Frame,
  //     },
  //     {
  //       name: "Quick link 2",
  //       url: "#",
  //       icon: PieChart,
  //     },
  //     {
  //       name: "Quick link 3",
  //       url: "#",
  //       icon: Map,
  //     },
  //   ],
  // };

  const getMenuItems = () => {
    let menu: SideBarItem[] = [];
    if (props.items) {
      menu = props.items;
    } else if (props.layout === "admin") {
      menu = AdminSidebar({ t, appConfiguration: rootData.appConfiguration });
    } else if (props.layout === "app") {
      menu = AppSidebar({
        t,
        tenantId: params.tenant ?? "",
        entities: appOrAdminData?.entities ?? [],
        entityGroups: appOrAdminData?.entityGroups ?? [],
        appConfiguration: rootData.appConfiguration,
      });
    } else if (props.layout === "docs") {
      menu = DocsSidebar();
    }

    function clearItemsIfNotCollapsible(items: SideBarItem[]) {
      items.forEach((item) => {
        if (item.isCollapsible !== undefined && !item.isCollapsible) {
          item.items = [];
        }
        if (item.items) {
          clearItemsIfNotCollapsible(item.items);
        }
      });
    }
    clearItemsIfNotCollapsible(menu);

    menu.forEach((item) => {
      if (item.isCollapsible !== undefined && !item.isCollapsible) {
        item.items = [];
      }
      item.items?.forEach((subitem) => {
        if (subitem.isCollapsible !== undefined && !subitem.isCollapsible) {
          subitem.items = [];
        }
      });
    });
    // setMenu(layout === "admin" ? AdminSidebar : );
    menu.forEach((group) => {
      group.items?.forEach((element) => {
        if (element.open || isCurrent(element) || currentIsChild(element)) {
          // expanded.push(element.path);
        } else {
          // setExpanded(expanded.filter((f) => f !== element.path));
        }
      });
    });
    return menu || [];
  };

  function getPath(item: SideBarItem) {
    return UrlUtils.replaceVariables(params, item.path) ?? "";
  }

  function isCurrent(menuItem: SideBarItem) {
    if (menuItem.path) {
      if (menuItem.exact) {
        return location?.pathname === getPath(menuItem);
      }
      return location?.pathname?.includes(getPath(menuItem));
    }
  }

  function currentIsChild(menuItem: SideBarItem) {
    let hasOpenChild = false;
    menuItem.items?.forEach((item) => {
      if (isCurrent(item)) {
        hasOpenChild = true;
      }
    });
    return hasOpenChild;
  }
  function allowCurrentUserType(item: SideBarItem) {
    if (!item.adminOnly) {
      return true;
    }
    return appData?.user?.admin !== null;
  }
  function allowCurrentTenantUserType(item: SideBarItem) {
    return !item.tenantUserTypes || item.tenantUserTypes.includes(appData?.currentRole);
  }
  function checkUserRolePermissions(item: SideBarItem) {
    return !item.permission || appOrAdminData?.permissions?.includes(item.permission) || appOrAdminData?.permissions?.includes(item.permission);
  }
  function checkFeatureFlags(item: SideBarItem) {
    return !item.featureFlag || rootData.featureFlags?.includes(item.featureFlag);
  }
  const getMenu = (): SidebarGroupDto[] => {
    function filterItem(f: SideBarItem) {
      return f.hidden !== true && allowCurrentUserType(f) && allowCurrentTenantUserType(f) && checkUserRolePermissions(f) && checkFeatureFlags(f);
    }
    const _menu: SidebarGroupDto[] = [];
    getMenuItems()
      .filter((f) => filterItem(f))
      .forEach((f) => {
        let type: "main" | "secondary" | "quick-link" = "main";
        if (f.isSecondary) {
          type = "secondary";
        } else if (f.isQuickLink) {
          type = "quick-link";
        }
        _menu.push({
          title: f.title.toString(),
          items:
            f.items
              ?.filter((f) => filterItem(f))
              .map((f) => {
                return {
                  ...f,
                  items: f.items?.filter((f) => filterItem(f)),
                };
              }) ?? [],
          type,
        });
      });
    return _menu.filter((f) => f.items.length > 0);
  };

  const navMainOld = getMenu().filter((f) => f.type === "main");
  const currentTab = navMainOld[0]?.items?.find((item) => findExactRoute(item, location.pathname));

  const customCurrentTab = currentTab &&
    (!isMainDashboard() || location.pathname.includes('/settings')) ? {
    ...currentTab,
    title: "Dashboard",
    isDashboard: true, // Add this flag to identify it as the dashboard item
    entityIcon: `<svg class="size-[18px]" width="18" height="18" viewBox="0 0 17 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12.7682 3.65365V4.98698H10.1016V3.65365H12.7682ZM6.10156 3.65365V7.65365H3.4349V3.65365H6.10156ZM12.7682 8.98698V12.987H10.1016V8.98698H12.7682ZM6.10156 11.6536V12.987H3.4349V11.6536H6.10156ZM14.1016 2.32031H8.76823V6.32031H14.1016V2.32031ZM7.4349 2.32031H2.10156V8.98698H7.4349V2.32031ZM14.1016 7.65365H8.76823V14.3203H14.1016V7.65365ZM7.4349 10.3203H2.10156V14.3203H7.4349V10.3203Z" fill="currentColor"></path></svg>`, // Dashboard icon
  } : null;



  const isGroupPath = location.pathname.includes('/g/');

  const shouldShowUngroupedEntities =
    location.pathname.includes('/dashboard') ||
    (!searchParams.has('group') && !isGroupPath);

  const shouldShowMainDashboard =
    location.pathname.includes('/dashboard') ||
    (!searchParams.has('group') && !isGroupPath);

  const navMain = shouldShowMainDashboard
    ? [
      {
        title: "Main Dashboard",
        items: [
          {
            title: "Main Dashboard",
            path: `/app/${appData?.currentTenant?.slug}/dashboard`,
            entityIcon: `<svg class="size-[18px]" width="18" height="18" viewBox="0 0 17 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12.7682 3.65365V4.98698H10.1016V3.65365H12.7682ZM6.10156 3.65365V7.65365H3.4349V3.65365H6.10156ZM12.7682 8.98698V12.987H10.1016V8.98698H12.7682ZM6.10156 11.6536V12.987H3.4349V11.6536H6.10156ZM14.1016 2.32031H8.76823V6.32031H14.1016V2.32031ZM7.4349 2.32031H2.10156V8.98698H7.4349V2.32031ZM14.1016 7.65365H8.76823V14.3203H14.1016V7.65365ZM7.4349 10.3203H2.10156V14.3203H7.4349V10.3203Z" fill="currentColor"></path></svg>`,
            isDashboard: true,
            items: [],
          },
          ...(shouldShowUngroupedEntities ? getUngroupedEntities() : [])
        ],
        type: "main" as const,
      },
    ]
    : groupsData[0]?.groups
      ?.filter((f) => {
        const groupSlugFromPath = location.pathname.split('/g/')[1]?.split('/')[0];
        return f.slug?.toLocaleLowerCase() === (groupSlugFromPath || selectGroupName)?.toLocaleLowerCase();
      })
      ?.map((group) => ({
        title: group?.title ?? "",
        items: [
          ...(customCurrentTab ? [customCurrentTab] : []),
          ...(group?.entities?.map((entity) => ({
            title: entity?.titlePlural ?? "",
            path: entity?.path ?? "",
            entityIcon: entity?.entityIcon ?? "",
            items: [],
          })) ?? []),
        ],
        type: "main" as const,
      })) || [];

  const navSecondary = getMenu().find((f) => f.type === "secondary") || { items: [] };
  const navQuickLinks = getMenu().find((f) => f.type === "quick-link");

  function isSecondLevelAdmin(pathname: string): boolean {
    const segments = pathname.split("/").filter(Boolean);
    return segments[0] === "admin";
  }
  React.useEffect(() => {
    // const isAdmin = isSecondLevelAdmin(location?.pathname);
    setIsAdmin(props.layout === "admin");
  }, [location?.pathname]);
  function onOpenCommandPalette() {
    query.toggle();
  }
  const NEW_NAV_THEME = true;
  const handleMouseEnter = React.useCallback(() => {
    if (state === "collapsed") {
      setIsHovering(true);
    }
  }, [state, setIsHovering]);
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);

  return (
    <Sidebar
      variant={DARK_SIDEBAR_IN_LIGHT_MODE ? "sidebar" : "inset"}
      collapsible={"icon"}
      {...props}
      className={cn(DARK_SIDEBAR_IN_LIGHT_MODE && "dark", !isAdmin && "transition-all duration-300 ease-in-out")}
      isDropdownOpen={isDropdownOpen}
    >
      <SidebarHeader>
        <SidebarMenu className={isCollapsed ? "py-[8.5px]" : ""}>
          {/* <SidebarMenu className={isCollapsed ? "!bg-yellow-500" : "!bg-violet-700"}> */}
          <SidebarMenuItem className={isCollapsed ? "flex justify-center" : ""}>
            <SidebarMenuButton size="lg" asChild className="hover:!rounded-none">
              <Link to={"/"} className="flex self-center">
                <div className={isCollapsed ? "flex items-center justify-center border-b border-transparent" : "shrink-0 !px-[4px]"}>
                  {isCollapsed ? (
                    <>
                      <Icon size=" md:h-9" />
                    </>
                  ) : (
                    <>
                      <Logo size=" h-[27px] md:h-9" />
                    </>
                  )}
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* </SidebarMenu> */}
        </SidebarMenu>

        {props.layout === "app" &&
          appData?.currentTenant &&
          (NEW_NAV_THEME ? (
            <CustomTeamSwitcher
              key={params.tenant}
              size="md"
              tenants={appData?.myTenants ?? []}
              groupsData={groupsData[0]}
              groupName={navMain[0]?.title || ""}
            />
          ) : (
            <TeamSwitcher key={params.tenant} size="md" tenants={appData?.myTenants ?? []} />
          ))}
      </SidebarHeader>
      <SidebarContent className="mt-3 overflow-x-hidden transition-all duration-300 ease-in-out" onMouseEnter={handleMouseEnter}>
        <NavMain items={isAdmin ? navMainOld : navMain} isAdmin={isAdmin} />
        <div className="mt-auto">
          {navQuickLinks && <NavQuickLinks item={navQuickLinks} />}
          {!isAdmin && (
            <div className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex !h-8 cursor-pointer items-center justify-start gap-2 rounded-sm px-2 py-2">
              <AddFeedbackButton showLabel={!isCollapsed} labelText="Feedback" className="" />
            </div>
          )}
          {!isAdmin && (
            <div className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex !h-8 cursor-pointer items-center justify-start gap-2 rounded-sm px-2 py-2">
              <SearchButton onClick={onOpenCommandPalette} showLabel={!isCollapsed} labelText="Global Search" className="" />
            </div>
          )}
          <NavSecondary item={navSecondary} />
        </div>
      </SidebarContent>
      <div className="bg-background">
        <Separator orientation="horizontal" decorative={true} className={cn("border-0.1 h-[1px] w-[219px]")} />
      </div>
      {appOrAdminData.user &&
        (isCollapsed ? (
          <SidebarFooter className="bg-background py-4" onMouseEnter={handleMouseEnter}>
            <NavUser layout={props.layout} user={appOrAdminData.user} setIsDropdownOpen={setIsDropdownOpen} isDropdownOpen={isDropdownOpen} />
          </SidebarFooter>
        ) : (
          <NavUser layout={props.layout} user={appOrAdminData.user} setIsDropdownOpen={setIsDropdownOpen} isDropdownOpen={isDropdownOpen} />
        ))}
    </Sidebar>
  );
}
