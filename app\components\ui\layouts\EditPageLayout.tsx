import { ReactNode, useState } from "react";
import { useLocation, useParams } from "react-router";
import BreadcrumbSimple from "../breadcrumbs/BreadcrumbSimple";
import Tabs, { TabItem } from "../tabs/Tabs";
import clsx from "clsx";
import { cn } from "~/lib/utils";
import BreadcrumbCustom from "~/custom/components/breadcrumbs/Breadcrumb";
import ActionsBar from "~/custom/components/ActionsBar/actionsBar";
import { useActionToggle } from "~/hooks/useActionToggle";
import { useIsMobile } from "~/hooks/use-mobile";
import ActionClosedIcon from "~/components/layouts/icons/ActionClosedIcon";

interface Props {
  title?: ReactNode;
  menu?: {
    title: string;
    routePath?: string;
  }[];
  buttons?: ReactNode;
  children: ReactNode;
  withHome?: boolean;
  tabs?: TabItem[];
  fullWidth?: boolean;
  className?: string;
  rowData?: any;
  options?: any;
  onSubmit?: (formData: FormData) => void;
  isActionsBarLoading?: boolean;
}
export default function EditPageLayout({ title, rowData, options, onSubmit, menu, buttons, children, withHome = true, tabs, fullWidth, className, isActionsBarLoading = false }: Props) {
  const params = useParams();
  const location = useLocation()
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isMobile = useIsMobile();
  const home = params.tenant ? `/app/${params.tenant}/dashboard` : "/admin/dashboard";
  const disableSideLayout = !(rowData?.entity?.hasTags || rowData?.entity?.hasTasks || rowData?.entity?.hasActivity);
  const { isActionOpen, toggleActionsBar, setIsActionOpen } = useActionToggle();
  console.log("isActionOpen", isActionOpen)
  const isAdminPage = location.pathname.includes("admin");
  return (
    <>
      <div
        className={cn(
          "transition-all duration-300 ease-in-out",
          isActionOpen && !isAdminPage && params?.id
            ? "grid sm:grid-cols-[73%_auto]"
            : !isAdminPage && !isActionOpen && params?.id
              ? "grid grid-cols-[97%_3%]"
              : "",
          // params?.id ? "sm:grid-cols-[73%_24%]" : "",
          isAdminPage
            ? cn(
              "mx-auto space-y-3 px-4 pt-3 sm:px-6 lg:px-8",
              fullWidth
                ? "w-full"
                : "max-w-5xl xl:max-w-7xl 2xl:max-w-[--breakpoint-2xl]"
            )
            : "h-screen"
        )}
      >
        <div className={cn(className, "mx-auto    ", fullWidth ? "w-full !pt-0  pl-0 pr-0" : !isAdminPage && params.id && !location.pathname.includes('edit') ? "w-full !pt-0  pl-0 pr-0" : "  mx-auto w-full max-w-5xl space-y-3 px-4 py-2 pb-6 sm:px-6 sm:pt-3 lg:px-8 xl:max-w-full")}>
          {(title || buttons || menu || tabs) && (
            // <div className="space-y-1">
            <>
              <div className={`flex items-center justify-between space-x-2 ${!isAdminPage ? "pl-[20px]" : ""}`}>
                {isAdminPage && <h1 className="flex flex-1 items-center truncate text-xl font-medium">{title}</h1>}
                <div className="flex items-center space-x-2">{buttons}</div>
              </div>
              {menu && isAdminPage && <BreadcrumbSimple home={withHome ? home : undefined} menu={menu} />}
              {tabs && <Tabs tabs={tabs} className="grow" />}
            </>
            // </div>
          )}

          {children}
        </div>

        <div className="">
          {rowData ? (
            <>

              <div className={clsx("flex flex-col  ", isActionOpen ? "sm:col-span-3 md:col-span-2 lg:col-span-2" : "sm:col-span-2 md:col-span-1 lg:col-span-1")}>

                {params.id && (
                  <div
                    className={clsx(
                      " h-screen   flex-shrink-0   border-l border-b  border-solid border-[#E5E5E5] border-opacity-10 bg-background  pb-5    sm:block  !border-l-[#E5E5E5]    ", !isAdminPage ? "absolute  top-0 w-[27%] " : "", isMobile ? "w-full top-[100px] z-40" : "",
                      isActionOpen ? "pr-1" : "",
                      !isActionOpen && isMobile ? "hidden" : "",
                    )}
                  >
                    <div className="relative">
                      <div
                        className={clsx(
                          "transition-all duration-300 ease-in-out",
                          isActionOpen ? "opacity-0 scale-95 pointer-events-none absolute" : "opacity-100 scale-100 pointer-events-auto relative"
                        )}
                      >
                        <div className="border-b border-solid border-[#E5E5E5] cursor-pointer ">
                          <button className="cursor-pointer pl-2 pt-[7.5px] pr-1" onClick={toggleActionsBar}>
                         <ActionClosedIcon/>
                          </button>
                        </div>
                      </div>

                      <div
                        className={clsx(
                          "transition-all duration-300 ease-in-out",
                          !isActionOpen ? "opacity-0 scale-95 pointer-events-none absolute" : "opacity-100 scale-100 relative"
                        )}
                      >
                        <ActionsBar
                          rowData={rowData}
                          options={options}
                          onSubmit={onSubmit}
                          onToggle={toggleActionsBar}
                          isActionOpen={isActionOpen}
                          isLoading={isActionsBarLoading}
                        />
                      </div>
                    </div>

                  </div>
                )}
              </div>
            </>
          ) : null}
        </div>


      </div>



    </>)


}
