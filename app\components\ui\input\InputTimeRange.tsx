//timeRange
import { forwardRef, Ref, useImperative<PERSON><PERSON><PERSON>, useRef, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { cn } from "~/lib/utils";
import HintTooltip from "../tooltips/HintTooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../select";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import { Clock } from "lucide-react";
import { Input } from "../input";

export interface RefInputTimeRange {
  input: HTMLInputElement | null;
}

interface Props {
  name?: string;
  title?: string;
  value?: { start: Date | string | undefined; end: Date | string | undefined };
  defaultValue?: { start: Date | string | undefined; end: Date | string | undefined };
  setValue?: React.Dispatch<React.SetStateAction<{ start: Date | undefined; end: Date | undefined } | undefined>>;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  icon?: string;
  autoFocus?: boolean;
  format?: "24h" | "12h";
  onChange?: (value: { start: Date | undefined; end: Date | undefined }) => void;
}

export default forwardRef(function InputTimeRange(
  {
    name,
    title,
    value,
    defaultValue,
    setValue,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    autoFocus = false,
    format = "24h",
    onChange,
  }: Props,
  ref: Ref<RefInputTimeRange>
) {
  const { t } = useTranslation();

  const input = useRef<HTMLInputElement>(null);
  const inputEnd = useRef<HTMLInputElement>(null);
  useImperativeHandle(ref, () => ({ input: input.current }), []);

  // Helper function to ensure dates are Date objects
  const ensureDateObject = (date: Date | string | undefined): Date | undefined => {
    if (!date) return undefined;
    if (date instanceof Date) return date;
    const dateObj = new Date(date);
    return isNaN(dateObj.getTime()) ? undefined : dateObj;
  };

  const [actualValue, setActualValue] = useState<{ start: Date | undefined; end: Date | undefined }>(() => {
    const initialValue = value || defaultValue || { start: undefined, end: undefined };
    return {
      start: ensureDateObject(initialValue.start),
      end: ensureDateObject(initialValue.end),
    };
  });

  // State for 12h format dropdowns - Start Time
  const [startHours, setStartHours] = useState<string>("12");
  const [startMinutes, setStartMinutes] = useState<string>("00");
  const [startMeridiem, setStartMeridiem] = useState<"AM" | "PM">("AM");

  // State for 12h format dropdowns - End Time
  const [endHours, setEndHours] = useState<string>("12");
  const [endMinutes, setEndMinutes] = useState<string>("00");
  const [endMeridiem, setEndMeridiem] = useState<"AM" | "PM">("PM");

  // Update actualValue when value prop changes
  useEffect(() => {
    if (value !== actualValue) {
      if (value) {
        setActualValue({
          start: ensureDateObject(value.start),
          end: ensureDateObject(value.end),
        });
      } else {
        setActualValue({ start: undefined, end: undefined });
      }
    }
  }, [value]);

  // Initialize from defaultValue
  useEffect(() => {
    if (defaultValue && !actualValue.start && !actualValue.end) {
      setActualValue({
        start: ensureDateObject(defaultValue.start),
        end: ensureDateObject(defaultValue.end),
      });
    }
  }, [defaultValue]);

  // Update dropdown components when actualValue changes
  useEffect(() => {
    if (format === "12h") {
      if (actualValue.start) {
        updateStartDropdownComponents(actualValue.start);
      }
      if (actualValue.end) {
        updateEndDropdownComponents(actualValue.end);
      }
    }
  }, [actualValue, format]);

  const updateStartDropdownComponents = (date: Date) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return;

    let hour12 = dateObj.getHours();
    const ampm = hour12 >= 12 ? "PM" : "AM";
    hour12 = hour12 % 12;
    if (hour12 === 0) hour12 = 12;

    setStartHours(hour12.toString().padStart(2, "0"));
    setStartMinutes(dateObj.getMinutes().toString().padStart(2, "0"));
    setStartMeridiem(ampm);
  };

  const updateEndDropdownComponents = (date: Date) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return;

    let hour12 = dateObj.getHours();
    const ampm = hour12 >= 12 ? "PM" : "AM";
    hour12 = hour12 % 12;
    if (hour12 === 0) hour12 = 12;

    setEndHours(hour12.toString().padStart(2, "0"));
    setEndMinutes(dateObj.getMinutes().toString().padStart(2, "0"));
    setEndMeridiem(ampm);
  };

  const createTimeFromComponents = (hours: string, minutes: string, meridiem: "AM" | "PM"): Date => {
    const today = new Date();
    let hour = parseInt(hours);
    const minute = parseInt(minutes);

    // Convert 12h to 24h format
    if (meridiem === "PM" && hour !== 12) hour += 12;
    if (meridiem === "AM" && hour === 12) hour = 0;

    today.setHours(hour, minute, 0, 0);
    return today;
  };

  const handleStartTimeChange = (newHours: string, newMinutes: string, newMeridiem: "AM" | "PM") => {
    const startTime = createTimeFromComponents(newHours, newMinutes, newMeridiem);
    const newValue = { ...actualValue, start: startTime };
    setActualValue(newValue);

    if (setValue) setValue(newValue);
    if (onChange) onChange(newValue);
  };

  const handleEndTimeChange = (newHours: string, newMinutes: string, newMeridiem: "AM" | "PM") => {
    const endTime = createTimeFromComponents(newHours, newMinutes, newMeridiem);
    const newValue = { ...actualValue, end: endTime };
    setActualValue(newValue);

    if (setValue) setValue(newValue);
    if (onChange) onChange(newValue);
  };

  const getTimeInputValue = (date: Date | undefined): string => {
    if (!date) return "";
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    const hours = dateObj.getHours().toString().padStart(2, "0");
    const minutes = dateObj.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const getHiddenInputValue = (date: Date | undefined): string => {
    if (!date) return "";
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    // Return ISO string for proper form submission
    return dateObj.toISOString();
  };

  const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>, isStart: boolean) => {
    const timeValue = e.target.value;
    if (!timeValue) return;

    const [hours, minutes] = timeValue.split(":");
    const today = new Date();
    today.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    const newValue = isStart ? { ...actualValue, start: today } : { ...actualValue, end: today };

    setActualValue(newValue);
    if (setValue) setValue(newValue);
    if (onChange) onChange(newValue);
  };

  const formatDisplayValue = (start: Date | undefined, end: Date | undefined): string => {
    if (!start || !end) return "";

    // Convert string dates to Date objects if needed
    const startDate = start instanceof Date ? start : new Date(start);
    const endDate = end instanceof Date ? end : new Date(end);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return "";

    const formatTime = (date: Date): string => {
      if (format === "12h") {
        let hours = date.getHours();
        const minutes = date.getMinutes().toString().padStart(2, "0");
        const ampm = hours >= 12 ? "PM" : "AM";
        hours = hours % 12;
        if (hours === 0) hours = 12;
        const formattedHours = hours.toString().padStart(2, "0");
        return `${formattedHours}:${minutes} ${ampm}`;
      } else {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      }
    };

    const startStr = formatTime(startDate);
    const endStr = formatTime(endDate);

    // Check if it's an overnight range
    const startMinutes = startDate.getHours() * 60 + startDate.getMinutes();
    const endMinutes = endDate.getHours() * 60 + endDate.getMinutes();
    const isOvernight = endMinutes <= startMinutes;

    return isOvernight ? `${startStr} – ${endStr} (+1 day)` : `${startStr} – ${endStr}`;
  };

  const validateTimeRange = (): string | null => {
    if (!actualValue.start || !actualValue.end) return null;

    // Convert string dates to Date objects if needed
    const startDate = actualValue.start instanceof Date ? actualValue.start : new Date(actualValue.start);
    const endDate = actualValue.end instanceof Date ? actualValue.end : new Date(actualValue.end);

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return null;

    const startMinutes = startDate.getHours() * 60 + startDate.getMinutes();
    const endMinutes = endDate.getHours() * 60 + endDate.getMinutes();

    // Allow overnight ranges - they're valid
    return null;
  };

  const validationError = validateTimeRange();

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <label htmlFor={name} className="text-foreground flex justify-between space-x-2 text-xs font-medium">
          <div className="flex items-center space-x-1">
            <div className="truncate">
              {title}
              {required && <span className="ml-1 text-red-500">*</span>}
            </div>
            {hint && <HintTooltip text={hint} />}
          </div>
        </label>
      )}

      <div className="relative mt-1">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 z-10 flex items-center pl-3">
            <EntityIcon className="text-muted-foreground h-4 w-4" icon={icon} />
          </div>
        )}

        {format === "24h" ? (
          <div className="flex items-center space-x-3">
            {name && (
              <>
                <input
                  type="hidden"
                  name={`${name}_start`}
                  value={getHiddenInputValue(actualValue.start)}
                  required={required && !actualValue.start}
                  disabled={actualValue.start === undefined}
                  hidden
                  readOnly
                />
                <input
                  type="hidden"
                  name={`${name}_end`}
                  value={getHiddenInputValue(actualValue.end)}
                  required={required && !actualValue.end}
                  disabled={actualValue.end === undefined}
                  hidden
                  readOnly
                />
              </>
            )}

            <Input
              ref={input}
              type="time"
              id={name}
              name={name}
              required={required}
              value={getTimeInputValue(actualValue.start)}
              onChange={(e) => handleTimeInputChange(e, true)}
              disabled={disabled || readOnly}
              readOnly={readOnly}
              autoFocus={autoFocus}
              className={cn(
                "focus-within:border-foreground focus:border-foreground placeholder:text-input rounded-md px-4 py-2.5 shadow-none focus:ring-0 focus:outline-none",
                (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed",
                icon && "pl-10"
              )}
            />

            {/* Separator */}
            <div className="flex items-center">
              <span className="text-muted-foreground text-lg font-medium">–</span>
            </div>

            <Input
              ref={inputEnd}
              type="time"
              id={name}
              name={name}
              required={required}
              value={getTimeInputValue(actualValue.end)}
              onChange={(e) => handleTimeInputChange(e, false)}
              disabled={disabled || readOnly}
              readOnly={readOnly}
              autoFocus={autoFocus}
              className={cn(
                "focus-within:border-foreground focus:border-foreground placeholder:text-input rounded-md shadow-none focus:ring-0 focus:outline-none",
                (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed",
                icon && "pl-10"
              )}
            />
          </div>
        ) : (
          <div className="flex items-center space-x-3">
            {name && (
              <>
                <input
                  type="hidden"
                  name={`${name}_start`}
                  value={getHiddenInputValue(actualValue.start)}
                  required={required && !actualValue.start}
                  disabled={actualValue.start === undefined}
                  hidden
                  readOnly
                />
                <input
                  type="hidden"
                  name={`${name}_end`}
                  value={getHiddenInputValue(actualValue.end)}
                  required={required && !actualValue.end}
                  disabled={actualValue.end === undefined}
                  hidden
                  readOnly
                />
              </>
            )}

            {/* Start Time Input */}
            <div
              className={cn(
                // "bg-background flex h-10 flex-1 items-center rounded-lg px-4 py-2.5 text-sm",
                // "focus-within:ring-ring/20 focus-within:border-ring transition-all duration-200 focus-within:ring-2",
                // "from-background to-background/95 bg-gradient-to-r shadow-sm",
                // (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
                // validationError && "border-red-500 focus-within:border-red-500 focus-within:ring-red-500/20"
                "border-input relative flex h-10 w-full items-center rounded-lg border px-4 py-2.5 text-sm",
                "focus-within:ring-ring/20 focus-within:border-ring transition-all duration-200 focus-within:ring-1",
                (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
                icon && "pl-10"
              )}
            >
              <div className="flex flex-1 items-center space-x-1">
                <Select
                  value={startHours}
                  onValueChange={(h) => {
                    setStartHours(h);
                    handleStartTimeChange(h, startMinutes, startMeridiem);
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="12" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((h) => (
                      <SelectItem key={h} value={h.toString().padStart(2, "0")}>
                        {h.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <span className="text-muted-foreground/70 text-sm font-medium">:</span>

                <Select
                  value={startMinutes}
                  onValueChange={(m) => {
                    setStartMinutes(m);
                    handleStartTimeChange(startHours, m, startMeridiem);
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="00" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 60 }, (_, i) => i).map((m) => (
                      <SelectItem key={m} value={m.toString().padStart(2, "0")}>
                        {m.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={startMeridiem}
                  onValueChange={(am) => {
                    setStartMeridiem(am as "AM" | "PM");
                    handleStartTimeChange(startHours, startMinutes, am as "AM" | "PM");
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-14 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="AM" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer">
                <Clock className="text-muted-foreground h-4 w-4" />
              </div>
            </div>

            {/* Separator */}
            <div className="flex items-center">
              <span className="text-muted-foreground text-lg">–</span>
            </div>

            {/* End Time Input */}
            <div
              className={cn(
                // "flex h-10 flex-1 items-center rounded-lg px-4 py-2.5 text-sm",
                // "focus-within:ring-ring/20 focus-within:border-ring transition-all duration-200 focus-within:ring-2",
                // "from-background to-background/95 bg-gradient-to-r shadow-sm",
                // (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
                // validationError && "border-red-500 focus-within:border-red-500 focus-within:ring-red-500/20"
                "border-input relative flex h-10 w-full items-center rounded-lg border px-4 py-2.5 text-sm",
                "focus-within:ring-ring/20 focus-within:border-ring transition-all duration-200 focus-within:ring-1",
                (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
                icon && "pl-10"
              )}
            >
              <div className="flex flex-1 items-center space-x-1">
                <Select
                  value={endHours}
                  onValueChange={(h) => {
                    setEndHours(h);
                    handleEndTimeChange(h, endMinutes, endMeridiem);
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="12" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((h) => (
                      <SelectItem key={h} value={h.toString().padStart(2, "0")}>
                        {h.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <span className="text-muted-foreground/70 text-sm font-medium">:</span>

                <Select
                  value={endMinutes}
                  onValueChange={(m) => {
                    setEndMinutes(m);
                    handleEndTimeChange(endHours, m, endMeridiem);
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="00" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 60 }, (_, i) => i).map((m) => (
                      <SelectItem key={m} value={m.toString().padStart(2, "0")}>
                        {m.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={endMeridiem}
                  onValueChange={(am) => {
                    setEndMeridiem(am as "AM" | "PM");
                    handleEndTimeChange(endHours, endMinutes, am as "AM" | "PM");
                  }}
                  disabled={disabled || readOnly}
                >
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-14 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="PM" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer">
                <Clock className="text-muted-foreground h-4 w-4" />
              </div>
            </div>
          </div>
        )}

        {actualValue.start && actualValue.end && (
          <div className="mt-2 px-1">
            <div className="text-muted-foreground/80 text-xs">
              {t("dateTime.preview")}: <span className="text-foreground/90">{formatDisplayValue(actualValue.start, actualValue.end)}</span>
            </div>
          </div>
        )}

        {validationError && <div className="mt-1 text-xs text-red-500">{validationError}</div>}
      </div>

      {help && <div className="text-muted-foreground mt-1 text-xs">{help}</div>}
    </div>
  );
});
