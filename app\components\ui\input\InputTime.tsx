//time
import clsx from "clsx";
import { forwardRef, Fragment, ReactNode, Ref, RefObject, useEffect, useImperativeHandle, useRef, useState } from "react";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import HintTooltip from "~/components/ui/tooltips/HintTooltip";
import { Clock } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Button } from "../button";
import { useTranslation } from "react-i18next";
import { Input } from "../input";
import { cn } from "~/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../select";
import { setHours, setMinutes } from "date-fns";

export interface RefInputTime {
  input: RefObject<HTMLInputElement | null>;
}

interface Props {
  display?: "default" | "picker";
  format?: "12h" | "24h";
  name?: string;
  title: string;
  defaultValue?: Date | undefined;
  value?: Date;
  onChange?: (date: Date) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: ReactNode;
  icon?: string;
  darkMode?: boolean;
  autoFocus?: boolean;
  classNameInput?: string;
  defaultTime?: string; // Format: "HH:mm" or "hh:mm AM/PM"
}

const InputTime = (
  {
    display = "default",
    format = "24h",
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    darkMode,
    autoFocus,
    classNameInput,
    defaultTime,
  }: Props,
  ref: Ref<RefInputTime>
) => {
  const { t } = useTranslation();
  useImperativeHandle(ref, () => ({ input }));
  const input = useRef<HTMLInputElement>(null);

  const [actualValue, setActualValue] = useState<Date | undefined>(value || defaultValue);
  const [isOpen, setIsOpen] = useState(false);

  // Get current time components from actualValue
  const getCurrentTimeComponents = () => {
    if (!actualValue) {
      return {
        hours: format === "12h" ? "12" : "00",
        minutes: "00",
        meridiem: "AM" as "AM" | "PM",
      };
    }

    const dateObj = actualValue instanceof Date ? actualValue : new Date(actualValue);
    if (isNaN(dateObj.getTime())) {
      return {
        hours: format === "12h" ? "12" : "00",
        minutes: "00",
        meridiem: "AM" as "AM" | "PM",
      };
    }

    const minutes = dateObj.getMinutes().toString().padStart(2, "0");

    if (format === "12h") {
      let hour12 = dateObj.getHours();
      const ampm = hour12 >= 12 ? "PM" : "AM";
      hour12 = hour12 % 12;
      if (hour12 === 0) hour12 = 12;
      const hours = hour12.toString().padStart(2, "0");
      return { hours, minutes, meridiem: ampm as "AM" | "PM" };
    } else {
      const hours = dateObj.getHours().toString().padStart(2, "0");
      return { hours, minutes, meridiem: "AM" as "AM" | "PM" };
    }
  };

  const { hours, minutes, meridiem } = getCurrentTimeComponents();

  // Update actualValue when value prop changes
  useEffect(() => {
    if (value !== actualValue) {
      setActualValue(value);
    }
  }, [actualValue, value]);

  console.log("actulavalue",actualValue)

  // Initialize from defaultTime
  useEffect(() => {
    const parseTimeString = (timeStr: string): Date | null => {
      if (!timeStr) return null;

      try {
        const today = new Date();
        today.setSeconds(0);
        today.setMilliseconds(0);

        if (format === "12h" && (timeStr.includes("AM") || timeStr.includes("PM"))) {
          const [time, ampm] = timeStr.split(" ");
          const [h, m] = time.split(":");
          let hour = parseInt(h);
          const minute = parseInt(m);

          if (isNaN(hour) || isNaN(minute)) return null;

          if (ampm === "PM" && hour !== 12) hour += 12;
          if (ampm === "AM" && hour === 12) hour = 0;

          today.setHours(hour, minute);
          return today;
        } else {
          const [h, m] = timeStr.split(":");
          const hour = parseInt(h);
          const minute = parseInt(m);

          if (isNaN(hour) || isNaN(minute)) return null;

          today.setHours(hour, minute);
          return today;
        }
      } catch {
        return null;
      }
    };
    if (defaultTime && !actualValue) {
      const timeDate = parseTimeString(defaultTime);
      if (timeDate) {
        setActualValue(timeDate);
      }
    }
  }, [actualValue, defaultTime, format]);


  const updateTime = () => {
    const today = new Date();
    today.setSeconds(0);
    today.setMilliseconds(0);

    let hour = parseInt(hours);
    const minute = parseInt(minutes);

    // Validate inputs
    if (isNaN(hour) || isNaN(minute)) return;
    if (minute < 0 || minute > 59) return;

    if (format === "12h") {
      if (hour < 1 || hour > 12) return;
      if (meridiem === "PM" && hour !== 12) hour += 12;
      if (meridiem === "AM" && hour === 12) hour = 0;
    } else {
      if (hour < 0 || hour > 23) return;
    }

    today.setHours(hour, minute);
    setActualValue(today);
  };

  const formatTimeDisplay = (date: Date): string => {
    // Ensure date is a Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    if (format === "12h") {
      return dateObj.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } else {
      return dateObj.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });
    }
  };

  const getTimeInputValue = (): string => {
    if (!actualValue) return "";
    console.log("actulavalue",actualValue)

    // Ensure actualValue is a Date object
    const dateValue = actualValue instanceof Date ? actualValue : new Date(actualValue);
    if (isNaN(dateValue.getTime())) return "";

    const hours = dateValue.getHours().toString().padStart(2, "0");
    const minutes = dateValue.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const timeValue = e.target.value;
    if (timeValue) {
      const [h, m] = timeValue.split(":");
      const today = new Date();
      today.setHours(parseInt(h), parseInt(m), 0, 0);
      setActualValue(today);
      if (onChange) {
        onChange(today);
      }
    } else {
      setActualValue(undefined);
      if (onChange) {
        onChange(undefined as any);
      }
    }
  };

  const handleHourChange = (newHour: string) => {
    updateTimeFromDropdowns(newHour, minutes, meridiem);
  };

  const handleMinuteChange = (newMinute: string) => {
    updateTimeFromDropdowns(hours, newMinute, meridiem);
  };

  const handleMeridiemChange = (newMeridiem: "AM" | "PM") => {
    updateTimeFromDropdowns(hours, minutes, newMeridiem);
  };

  const updateTimeFromDropdowns = (h: string, m: string, ampm: "AM" | "PM") => {
    const today = new Date();
    today.setSeconds(0);
    today.setMilliseconds(0);

    let hour = parseInt(h);
    const minute = parseInt(m);

    // Validate inputs
    if (isNaN(hour) || isNaN(minute)) return;
    if (minute < 0 || minute > 59) return;
    if (hour < 1 || hour > 12) return;

    if (ampm === "PM" && hour !== 12) hour += 12;
    if (ampm === "AM" && hour === 12) hour = 0;

    today.setHours(hour, minute);
    const newValue = today;
    setActualValue(newValue);

    // Notify parent immediately
    if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <div className={clsx(className, !darkMode && "")}>
      <label htmlFor={name} className="flex justify-between space-x-2 text-xs font-medium">
        <div className="flex items-center space-x-1">
          <div className="truncate">
            {title}
            {required && <span className="text-destructive ml-1">*</span>}
          </div>
          {help && <HintTooltip text={help} />}
        </div>
        {hint}
      </label>
      <div className="relative mt-1 space-y-1">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <EntityIcon className="text-muted-foreground h-4 w-4" icon={icon} />
          </div>
        )}

        {/* ===== Default 24h Input ===== */}
        {display === "default" && format === "24h" && (
          <Input
            ref={input}
            type="time"
            id={name}
            name={name}
            required={required}
            value={getTimeInputValue()}
            onChange={handleTimeInputChange}
            disabled={disabled || readOnly}
            readOnly={readOnly}
            autoFocus={autoFocus}
            className={cn("rounded-md", (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed", icon && "pl-10", classNameInput)}
          />
        )}

        {/* ===== Default 12h Input ===== */}
        {display === "default" && format === "12h" && (
          <Fragment>
            {name && <input type="hidden" name={name} value={getTimeInputValue()} required={required} disabled={actualValue === undefined} hidden readOnly />}
            <div
              className={cn(
                "border-border/60 flex h-10 w-full items-center rounded-lg border px-4 py-2.5 text-sm",
                "focus-within:ring-ring/20 focus-within:border-ring transition-all duration-200 focus-within:ring-1",
                (disabled || readOnly) && "bg-secondary/90 cursor-not-allowed opacity-50",
                icon && "pl-10",
                classNameInput
              )}
            >
              {/* Time Icon */}
              <div className="flex flex-1 items-center space-x-1">
                <Select value={hours} onValueChange={handleHourChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="12" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((h) => (
                      <SelectItem key={h} value={h.toString().padStart(2, "0")}>
                        {h.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <span className="text-muted-foreground/70 text-sm font-medium">:</span>

                <Select value={minutes} onValueChange={handleMinuteChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-12 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="00" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    {Array.from({ length: 60 }, (_, i) => i).map((m) => (
                      <SelectItem key={m} value={m.toString().padStart(2, "0")}>
                        {m.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={meridiem} onValueChange={handleMeridiemChange} disabled={disabled || readOnly}>
                  <SelectTrigger className="hover:bg-accent/50 h-auto w-14 rounded border-0 bg-transparent p-1 text-sm shadow-none transition-colors">
                    <SelectValue placeholder="AM" />
                  </SelectTrigger>
                  <SelectContent align="start">
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Clock Icon */}
              <div className="absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer">
                <Clock className="text-muted-foreground h-4 w-4" />
              </div>
            </div>
          </Fragment>
        )}

        {/* ===== Picker Display (Popover) ===== */}
        {display === "picker" && (
          <Fragment>
            {name && <input type="hidden" name={name} value={getTimeInputValue()} required={required} disabled={actualValue === undefined} hidden readOnly />}
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={clsx("w-full justify-start text-left font-normal", !actualValue && "text-muted-foreground")}
                  disabled={disabled || readOnly}
                  autoFocus={autoFocus}
                >
                  <Clock className="text-muted-foreground mr-2 h-4 w-4" />
                  {actualValue ? formatTimeDisplay(actualValue) : t("components.time.pick")}
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-[320px] rounded-md border p-4 shadow-lg">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    {/* Hour */}
                    <div className="flex-1">
                      <label className="text-muted-foreground text-xs font-medium">{format === "12h" ? "Hour" : "Hour (24h)"}</label>
                      <Select value={hours} onValueChange={setHours}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {(format === "12h" ? Array.from({ length: 12 }, (_, i) => i + 1) : Array.from({ length: 24 }, (_, i) => i)).map((h) => (
                            <SelectItem key={h} value={h.toString().padStart(2, "0")}>
                              {h.toString().padStart(2, "0")}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Minute */}
                    <div className="flex-1">
                      <label className="text-muted-foreground text-xs font-medium">Minute</label>
                      <Select value={minutes} onValueChange={setMinutes}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 60 }, (_, i) => i).map((m) => (
                            <SelectItem key={m} value={m.toString().padStart(2, "0")}>
                              {m.toString().padStart(2, "0")}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Meridiem (if 12h) */}
                    {format === "12h" && (
                      <div className="flex-1">
                        <label className="text-muted-foreground text-xs font-medium">AM/PM</label>
                        <Select value={meridiem} onValueChange={(value: "AM" | "PM") => setMeridiem(value)}>
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AM">AM</SelectItem>
                            <SelectItem value="PM">PM</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end gap-2 pt-2">
                    <Button variant="outline" size="sm" onClick={() => setIsOpen(false)}>
                      {t("shared.cancel")}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => {
                        updateTime();
                        setIsOpen(false);
                      }}
                    >
                      {t("shared.save")}
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </Fragment>
        )}
      </div>
    </div>
  );
};

export default forwardRef(InputTime);
